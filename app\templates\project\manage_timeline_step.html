{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <!-- Header -->
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            إدارة الخطوة - {{ step.title }}
                        </h4>
                        <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>العودة للتايم لاين
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h5>{{ step.title }}</h5>
                            {% if step.description %}
                            <p class="text-muted">{{ step.description }}</p>
                            {% endif %}
                            
                            <div class="mb-3">
                                <span class="badge bg-{{ step.get_status_color() }} me-2">
                                    <i class="{{ step.get_status_icon() }} me-1"></i>
                                    {{ step.get_status_display() }}
                                </span>
                                
                                {% if step.source_type == 'task' %}
                                <span class="badge bg-info">
                                    <i class="fas fa-tasks me-1"></i>
                                    مرتبط بمهمة
                                </span>
                                {% else %}
                                <span class="badge bg-warning">
                                    <i class="fas fa-edit me-1"></i>
                                    خطوة يدوية
                                </span>
                                {% endif %}
                            </div>
                            
                            {% if step.get_links_list() %}
                            <div class="mb-3">
                                <h6>الروابط:</h6>
                                {% for link in step.get_links_list() %}
                                <a href="{{ link }}" target="_blank" class="btn btn-sm btn-outline-primary me-1 mb-1">
                                    <i class="fas fa-external-link-alt me-1"></i>رابط {{ loop.index }}
                                </a>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">معلومات الخطوة</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            {% if step.source_type == 'task' and step.task %}
                                                صاحب المهمة: {{ step.task.assigned_to.get_full_name() if step.task.assigned_to else 'غير محدد' }}
                                            {% else %}
                                                منشئ الخطوة: {{ step.created_by.get_full_name() }}
                                            {% endif %}
                                        </small>
                                    </p>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            تاريخ الإنشاء: {{ step.created_at.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </p>
                                    {% if step.source_type == 'task' and step.task %}
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <i class="fas fa-link me-1"></i>
                                            <a href="{{ url_for('project.view_task', task_id=step.task.id) }}" target="_blank">
                                                عرض المهمة المرتبطة
                                            </a>
                                        </small>
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="row">
                {% if step.source_type == 'manual' %}
                <!-- Manual Step Actions -->
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exchange-alt me-2"></i>
                                تغيير حالة الخطوة
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">يمكنك تغيير حالة هذه الخطوة اليدوية:</p>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-secondary {{ 'active' if step.status == 'pending' else '' }}" 
                                        onclick="updateStepStatus('{{ step.id }}', 'pending')">
                                    <i class="fas fa-clock me-2"></i>معلق
                                    {% if step.status == 'pending' %}<i class="fas fa-check float-end"></i>{% endif %}
                                </button>
                                
                                <button class="btn btn-outline-primary {{ 'active' if step.status == 'in_progress' else '' }}" 
                                        onclick="updateStepStatus('{{ step.id }}', 'in_progress')">
                                    <i class="fas fa-spinner me-2"></i>قيد التنفيذ
                                    {% if step.status == 'in_progress' %}<i class="fas fa-check float-end"></i>{% endif %}
                                </button>
                                
                                <button class="btn btn-outline-success {{ 'active' if step.status == 'completed' else '' }}" 
                                        onclick="updateStepStatus('{{ step.id }}', 'completed')">
                                    <i class="fas fa-check-circle me-2"></i>مكتمل
                                    {% if step.status == 'completed' %}<i class="fas fa-check float-end"></i>{% endif %}
                                </button>
                                
                                <button class="btn btn-outline-danger {{ 'active' if step.status == 'cancelled' else '' }}" 
                                        onclick="updateStepStatus('{{ step.id }}', 'cancelled')">
                                    <i class="fas fa-times-circle me-2"></i>ملغي
                                    {% if step.status == 'cancelled' %}<i class="fas fa-check float-end"></i>{% endif %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- Task-linked Step Info -->
                <div class="col-md-6">
                    <div class="card shadow">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                خطوة مرتبطة بمهمة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> هذه الخطوة مرتبطة بمهمة. حالة الخطوة يتم تحديثها تلقائياً من حالة المهمة.
                            </div>
                            
                            {% if step.task %}
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">{{ step.task.title }}</h6>
                                    <p class="card-text">{{ step.task.description or 'لا يوجد وصف' }}</p>
                                    <span class="badge bg-{{ step.task.get_status_color() }}">
                                        {{ step.task.get_status_display() }}
                                    </span>
                                    <div class="mt-2">
                                        <a href="{{ url_for('project.view_task', task_id=step.task.id) }}"
                                           class="btn btn-sm btn-primary" target="_blank">
                                            <i class="fas fa-external-link-alt me-1"></i>عرض المهمة
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Danger Zone -->
                {% if current_user.has_role('admin') or current_user.has_role('manager') or
                      current_user in project.managers or current_user.id == project.manager_id %}
                <div class="col-md-6">
                    <div class="card shadow border-danger">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                منطقة خطر
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="text-muted">الإجراءات التالية لا يمكن التراجع عنها:</p>
                            
                            <button class="btn btn-danger" onclick="deleteStep('{{ step.id }}')">
                                <i class="fas fa-trash me-2"></i>حذف الخطوة نهائياً
                            </button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updateStepStatus(stepId, newStatus) {
    const formData = new FormData();
    formData.append('status', newStatus);
    
    fetch(`{{ url_for('project.update_timeline_step_status', step_id=0) }}`.replace('0', stepId), {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Refresh page after 1 second
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء تحديث حالة الخطوة');
    });
}

function deleteStep(stepId) {
    if (confirm('هل أنت متأكد من حذف هذه الخطوة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ url_for('project.delete_timeline_step', step_id=0) }}`.replace('0', stepId);
        document.body.appendChild(form);
        form.submit();
    }
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
