"""
نماذج النظام المالي - الرواتب والمعاملات
"""

from datetime import datetime
from app import db
from sqlalchemy import func, Numeric
import json

class PayrollEntry(db.Model):
    """نموذج رواتب الموظفين"""
    __tablename__ = 'payroll_entries'

    id = db.Column(db.Integer, primary_key=True)
    employee_name = db.Column(db.String(100), nullable=False)  # اسم الموظف
    employee_user_id = db.Column(db.Integer, db.ForeignKey('users.id'))  # المستخدم المرتبط (اختياري)
    amount = db.Column(Numeric(15, 2), nullable=False)  # مبلغ الراتب
    status = db.Column(db.String(20), nullable=False, default='draft')  # حالة الراتب
    transfer_date = db.Column(db.DateTime)  # موعد الحوالة
    supervisor_id = db.Column(db.Integer, db.<PERSON>ey('users.id'))  # الموظف المشرف

    # الحسابات المرتبطة
    debit_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الخصم
    pending_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب المعلق
    paid_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب المدفوع

    # المشاريع والمهام المرتبطة
    related_projects = db.Column(db.Text)  # JSON للمشاريع المرتبطة
    related_tasks = db.Column(db.Text)  # JSON للمهام المرتبطة

    # المرفقات
    links = db.Column(db.Text)  # JSON للروابط
    attachments = db.Column(db.Text)  # JSON للمرفقات

    notes = db.Column(db.Text)  # ملاحظات

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # العلاقات
    employee_user = db.relationship('User', foreign_keys=[employee_user_id], backref='payroll_entries')
    supervisor = db.relationship('User', foreign_keys=[supervisor_id], backref='supervised_payrolls')
    debit_account = db.relationship('Account', foreign_keys=[debit_account_id])
    pending_account = db.relationship('Account', foreign_keys=[pending_account_id])
    paid_account = db.relationship('Account', foreign_keys=[paid_account_id])
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_payrolls')

    # الفهارس
    __table_args__ = (
        db.Index('idx_payroll_status', 'status'),
        db.Index('idx_payroll_employee', 'employee_name'),
        db.Index('idx_payroll_date', 'transfer_date'),
    )

    def __repr__(self):
        return f'<PayrollEntry {self.employee_name}: {self.amount}>'

    @property
    def status_display(self):
        """عرض حالة الراتب"""
        status_map = {
            'draft': 'مسودة',
            'pending': 'معلق',
            'paid': 'مدفوع',
            'cancelled': 'ملغي'
        }
        return status_map.get(self.status, self.status)

    @property
    def status_color(self):
        """لون حالة الراتب"""
        color_map = {
            'draft': 'secondary',
            'pending': 'warning',
            'paid': 'success',
            'cancelled': 'danger'
        }
        return color_map.get(self.status, 'secondary')

    def get_related_projects(self):
        """الحصول على المشاريع المرتبطة"""
        if self.related_projects:
            try:
                project_ids = json.loads(self.related_projects)
                from app.models.project import Project
                return Project.query.filter(Project.id.in_(project_ids)).all()
            except:
                return []
        return []

    def set_related_projects(self, project_ids):
        """تعيين المشاريع المرتبطة"""
        self.related_projects = json.dumps(project_ids) if project_ids else None

    def get_related_tasks(self):
        """الحصول على المهام المرتبطة"""
        if self.related_tasks:
            try:
                task_ids = json.loads(self.related_tasks)
                from app.models.task import Task
                return Task.query.filter(Task.id.in_(task_ids)).all()
            except:
                return []
        return []

    def set_related_tasks(self, task_ids):
        """تعيين المهام المرتبطة"""
        self.related_tasks = json.dumps(task_ids) if task_ids else None

    def get_links(self):
        """الحصول على الروابط"""
        if self.links:
            try:
                return json.loads(self.links)
            except:
                return []
        return []

    def set_links(self, links_list):
        """تعيين الروابط"""
        self.links = json.dumps(links_list) if links_list else None

    def get_attachments(self):
        """الحصول على المرفقات"""
        if self.attachments:
            try:
                return json.loads(self.attachments)
            except:
                return []
        return []

    def set_attachments(self, attachments_list):
        """تعيين المرفقات"""
        self.attachments = json.dumps(attachments_list) if attachments_list else None

    def update_status(self, new_status):
        """تحديث حالة الراتب مع تحديث الحسابات"""
        old_status = self.status

        # إذا كانت الحالة نفسها، لا نفعل شيء
        if old_status == new_status:
            return

        # التراجع عن التأثير السابق
        if old_status == 'pending' and self.pending_account:
            self.pending_account.update_balance(self.amount, 'subtract')
        elif old_status == 'paid':
            if self.pending_account:
                self.pending_account.update_balance(self.amount, 'add')
            if self.debit_account:
                self.debit_account.update_balance(self.amount, 'add')
            if self.paid_account:
                self.paid_account.update_balance(self.amount, 'subtract')

        # تطبيق التأثير الجديد
        if new_status == 'pending' and self.pending_account:
            self.pending_account.update_balance(self.amount, 'add')
        elif new_status == 'paid':
            if old_status == 'pending' and self.pending_account:
                self.pending_account.update_balance(self.amount, 'subtract')
            if self.debit_account:
                self.debit_account.update_balance(self.amount, 'subtract')
            if self.paid_account:
                self.paid_account.update_balance(self.amount, 'add')

        # تحديث الحالة
        self.status = new_status
        self.updated_at = datetime.utcnow()
        db.session.commit()

class Transaction(db.Model):
    """نموذج المعاملات المالية"""
    __tablename__ = 'financial_transactions'

    id = db.Column(db.Integer, primary_key=True)
    transaction_number = db.Column(db.String(50), unique=True, nullable=False)  # رقم المعاملة
    name = db.Column(db.String(200), nullable=False)  # اسم المعاملة
    description = db.Column(db.Text)  # وصف المعاملة
    transaction_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)  # تاريخ المعاملة
    status = db.Column(db.String(20), nullable=False, default='draft')  # حالة المعاملة

    # المرفقات
    links = db.Column(db.Text)  # JSON للروابط
    attachments = db.Column(db.Text)  # JSON للمرفقات

    notes = db.Column(db.Text)  # ملاحظات

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # العلاقات
    created_by = db.relationship('User', backref='created_financial_transactions')
    items = db.relationship('TransactionItem', backref='transaction', cascade='all, delete-orphan')

    # الفهارس
    __table_args__ = (
        db.Index('idx_transaction_number', 'transaction_number'),
        db.Index('idx_transaction_status', 'status'),
        db.Index('idx_transaction_date', 'transaction_date'),
    )

    def __repr__(self):
        return f'<Transaction {self.transaction_number}: {self.name}>'

    @property
    def status_display(self):
        """عرض حالة المعاملة"""
        status_map = {
            'draft': 'مسودة',
            'pending': 'معلقة',
            'paid': 'مدفوعة',
            'cancelled': 'ملغية'
        }
        return status_map.get(self.status, self.status)

    @property
    def status_color(self):
        """لون حالة المعاملة"""
        color_map = {
            'draft': 'secondary',
            'pending': 'warning',
            'paid': 'success',
            'cancelled': 'danger'
        }
        return color_map.get(self.status, 'secondary')

    @property
    def total_amount(self):
        """إجمالي مبلغ المعاملة"""
        return sum(item.amount for item in self.items)

    def get_links(self):
        """الحصول على الروابط"""
        if self.links:
            try:
                return json.loads(self.links)
            except:
                return []
        return []

    def set_links(self, links_list):
        """تعيين الروابط"""
        self.links = json.dumps(links_list) if links_list else None

    def get_attachments(self):
        """الحصول على المرفقات"""
        if self.attachments:
            try:
                return json.loads(self.attachments)
            except:
                return []
        return []

    def set_attachments(self, attachments_list):
        """تعيين المرفقات"""
        self.attachments = json.dumps(attachments_list) if attachments_list else None

    def update_status(self, new_status):
        """تحديث حالة المعاملة مع تحديث الحسابات"""
        old_status = self.status

        # إذا كانت الحالة نفسها، لا نفعل شيء
        if old_status == new_status:
            return

        # التراجع عن التأثير السابق
        if old_status in ['pending', 'paid']:
            for item in self.items:
                if item.account:
                    if item.operation == 'add':
                        item.account.update_balance(item.amount, 'subtract')
                    elif item.operation == 'subtract':
                        item.account.update_balance(item.amount, 'add')

        # تطبيق التأثير الجديد
        if new_status in ['pending', 'paid']:
            for item in self.items:
                if item.account:
                    if item.operation == 'add':
                        item.account.update_balance(item.amount, 'add')
                    elif item.operation == 'subtract':
                        item.account.update_balance(item.amount, 'subtract')

        # تحديث الحالة
        self.status = new_status
        self.updated_at = datetime.utcnow()
        db.session.commit()

class TransactionItem(db.Model):
    """نموذج بنود المعاملة"""
    __tablename__ = 'transaction_items'

    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.Integer, db.ForeignKey('financial_transactions.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'), nullable=False)
    operation = db.Column(db.String(20), nullable=False)  # add أو subtract
    amount = db.Column(Numeric(15, 2), nullable=False)  # مبلغ البند
    item_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)  # تاريخ البند
    description = db.Column(db.Text)  # وصف البند

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    account = db.relationship('Account', backref='transaction_items')

    # الفهارس
    __table_args__ = (
        db.Index('idx_transaction_item_account', 'account_id'),
        db.Index('idx_transaction_item_operation', 'operation'),
    )

    def __repr__(self):
        return f'<TransactionItem {self.account.name}: {self.operation} {self.amount}>'

    @property
    def operation_display(self):
        """عرض نوع العملية"""
        operation_map = {
            'add': 'إضافة',
            'subtract': 'خصم'
        }
        return operation_map.get(self.operation, self.operation)

# الاحتفاظ بنموذج الفواتير الموجود مع التعديلات المطلوبة

class Invoice(db.Model):
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(20), unique=True, nullable=False)
    issue_date = db.Column(db.DateTime, default=datetime.utcnow)
    due_date = db.Column(db.DateTime)
    approval_date = db.Column(db.DateTime)

    # Nuevos campos para comisiones, impuestos y descuentos
    transfer_fee_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    transfer_fee_value = db.Column(db.Float, default=0)
    tax_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    tax_value = db.Column(db.Float, default=0)
    discount_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    discount_value = db.Column(db.Float, default=0)

    subtotal = db.Column(db.Float, default=0)  # Suma de los elementos de línea
    total_amount = db.Column(db.Float, default=0)  # Total final después de comisiones, impuestos y descuentos

    status = db.Column(db.String(20), default='unpaid')  # unpaid, paid, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    project_id = db.Column(db.Integer, db.ForeignKey('projects.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_by = db.relationship('User', backref='created_invoices')

    # Invoice items
    items = db.relationship('InvoiceItem', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Invoice attachments and verification links
    attachments = db.relationship('InvoiceAttachment', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')
    verification_links = db.relationship('InvoiceVerificationLink', backref='invoice', lazy='dynamic', cascade='all, delete-orphan')

    # Transactions related to this invoice (commented out for now)
    # transactions = db.relationship('Transaction', backref='invoice', lazy='dynamic')

    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'

    def is_overdue(self):
        if self.due_date and self.status == 'unpaid':
            return datetime.utcnow() > self.due_date
        return False

    def calculate_subtotal(self):
        """Calcula el subtotal sumando todos los elementos de línea"""
        return sum(item.total_price for item in self.items)

    def calculate_transfer_fee(self):
        """Calcula la comisión de transferencia"""
        if self.transfer_fee_type == 'percentage':
            return (self.transfer_fee_value / 100) * self.subtotal
        else:
            return self.transfer_fee_value

    def calculate_tax(self):
        """Calcula el impuesto"""
        if self.tax_type == 'percentage':
            return (self.tax_value / 100) * self.subtotal
        else:
            return self.tax_value

    def calculate_discount(self):
        """Calcula el descuento"""
        if self.discount_type == 'percentage':
            return (self.discount_value / 100) * self.subtotal
        else:
            return self.discount_value

    def calculate_total(self):
        """Calcula el total final"""
        self.subtotal = self.calculate_subtotal()
        transfer_fee = self.calculate_transfer_fee()
        tax = self.calculate_tax()
        discount = self.calculate_discount()

        total = self.subtotal + transfer_fee + tax - discount
        return max(0, total)  # Asegurarse de que el total no sea negativo

    def update_total(self):
        """Actualiza los campos subtotal y total_amount"""
        self.subtotal = self.calculate_subtotal()
        self.total_amount = self.calculate_total()

    def mark_as_paid(self):
        self.status = 'paid'
        # Create a transaction for this payment
        # El beneficio de la empresa ya está incluido en el total_amount
        transaction = Transaction(
            amount=self.total_amount,
            transaction_type='income',
            category='invoice_payment',
            description=f'Payment for invoice {self.invoice_number}',
            invoice_id=self.id
        )
        db.session.add(transaction)

class InvoiceItem(db.Model):
    __tablename__ = 'invoice_items'

    id = db.Column(db.Integer, primary_key=True)
    description = db.Column(db.String(255), nullable=False)
    quantity = db.Column(db.Float, default=1)
    unit_price = db.Column(db.Float, nullable=False)

    # Nuevos campos
    company_profit_type = db.Column(db.String(10), default='percentage')  # percentage, fixed
    company_profit_value = db.Column(db.Float, default=0)

    # Campos adicionales solicitados
    status = db.Column(db.String(20), default='غير مستلم')  # مستلم, غير مستلم
    receipt_date = db.Column(db.DateTime)  # Fecha de recepción (opcional)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    supervisor_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    supervisor = db.relationship('User', backref='supervised_invoice_items')

    @property
    def total_price(self):
        return self.quantity * self.unit_price

    def calculate_company_profit(self):
        """Calcula el beneficio de la empresa para este elemento"""
        if self.company_profit_type == 'percentage':
            return (self.company_profit_value / 100) * self.total_price
        else:
            return self.company_profit_value

    @property
    def employee_profit(self):
        """Calcula el beneficio del empleado para este elemento (total - beneficio de la empresa)"""
        return self.total_price - self.calculate_company_profit()

    def __repr__(self):
        return f'<InvoiceItem {self.description}>'


class InvoiceAttachment(db.Model):
    __tablename__ = 'invoice_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'))
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    uploaded_by = db.relationship('User', backref='uploaded_attachments')

    def __repr__(self):
        return f'<InvoiceAttachment {self.filename}>'


class InvoiceVerificationLink(db.Model):
    __tablename__ = 'invoice_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='invoice_verification_links')

    def __repr__(self):
        return f'<InvoiceVerificationLink {self.url}>'


class TransactionAttachment(db.Model):
    __tablename__ = 'transaction_attachments'

    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    file_type = db.Column(db.String(50))
    file_size = db.Column(db.Integer)  # Size in bytes
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    uploaded_by = db.relationship('User', backref='transaction_attachments')

    def __repr__(self):
        return f'<TransactionAttachment {self.filename}>'


class TransactionVerificationLink(db.Model):
    __tablename__ = 'transaction_verification_links'

    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(500), nullable=False)
    description = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    transaction_id = db.Column(db.Integer, db.ForeignKey('transactions.id'), nullable=False)
    added_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    added_by = db.relationship('User', backref='transaction_verification_links')

    def __repr__(self):
        return f'<TransactionVerificationLink {self.url}>'
