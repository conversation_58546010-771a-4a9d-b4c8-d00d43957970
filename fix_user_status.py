#!/usr/bin/env python3
"""
Script to fix user status fields
"""

from app import create_app, db
from sqlalchemy import text
from datetime import datetime

def fix_user_status():
    """Fix user status fields"""
    try:
        print("Fixing user status fields...")
        
        # Update all users to have default status if null
        result = db.session.execute(text("UPDATE users SET status = 'offline' WHERE status IS NULL OR status = ''"))
        print(f"Updated {result.rowcount} users with default status")
        
        # Update all users to have default last_activity if null
        result = db.session.execute(text("UPDATE users SET last_activity = CURRENT_TIMESTAMP WHERE last_activity IS NULL"))
        print(f"Updated {result.rowcount} users with default last_activity")
        
        # Check current status of users
        result = db.session.execute(text("SELECT id, username, status, custom_status, last_activity FROM users LIMIT 5"))
        print("\nCurrent user status:")
        for row in result.fetchall():
            print(f"User {row[1]}: status={row[2]}, custom_status={row[3]}, last_activity={row[4]}")
        
        db.session.commit()
        print("\n✅ User status fields fixed successfully")
        
    except Exception as e:
        print(f"❌ Error fixing user status fields: {e}")
        db.session.rollback()
        return False
    
    return True

def main():
    """Main function"""
    print("🔧 Fixing user status fields...")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        success = fix_user_status()
        
        if success:
            print("\n🎉 User status fix completed successfully!")
            return True
        else:
            print("\n💥 User status fix failed!")
            return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
