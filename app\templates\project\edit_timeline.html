{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-edit me-2"></i>
                            تعديل التايم لاين
                        </h4>
                        <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>العودة للتايم لاين
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                اسم التايم لاين <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ timeline.name }}" required maxlength="100"
                                   placeholder="أدخل اسم التايم لاين">
                            <div class="form-text">اسم واضح ومختصر للتايم لاين</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-1"></i>
                                الوصف
                            </label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="4" placeholder="وصف مفصل للتايم لاين (اختياري)">{{ timeline.description or '' }}</textarea>
                            <div class="form-text">وصف مفصل لأهداف ومحتوى التايم لاين</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle me-1"></i>
                                            معلومات التايم لاين
                                        </h6>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-project-diagram me-1"></i>
                                                المشروع: {{ project.name }}
                                            </small>
                                        </p>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-list me-1"></i>
                                                عدد الخطوات: {{ timeline.steps.count() }}
                                            </small>
                                        </p>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                تاريخ الإنشاء: {{ timeline.created_at.strftime('%Y-%m-%d %H:%M') }}
                                            </small>
                                        </p>
                                        <p class="card-text">
                                            <small class="text-muted">
                                                <i class="fas fa-user me-1"></i>
                                                منشئ التايم لاين: {{ timeline.created_by.get_full_name() }}
                                            </small>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card bg-warning bg-opacity-10 border-warning">
                                    <div class="card-body">
                                        <h6 class="card-title text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            تنبيه
                                        </h6>
                                        <p class="card-text">
                                            <small>
                                                تعديل اسم أو وصف التايم لاين لن يؤثر على الخطوات الموجودة.
                                                جميع الخطوات ستبقى كما هي.
                                            </small>
                                        </p>
                                        
                                        {% if timeline.steps.count() > 0 %}
                                        <div class="mt-2">
                                            <small class="text-muted">الخطوات الحالية:</small>
                                            <ul class="list-unstyled mt-1">
                                                {% for step in timeline.steps.limit(3) %}
                                                <li class="small">
                                                    <i class="fas fa-dot-circle me-1 text-{{ step.get_status_color() }}"></i>
                                                    {{ step.title }}
                                                </li>
                                                {% endfor %}
                                                {% if timeline.steps.count() > 3 %}
                                                <li class="small text-muted">
                                                    ... و {{ timeline.steps.count() - 3 }} خطوة أخرى
                                                </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    حفظ التغييرات
                                </button>
                                <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times me-1"></i>
                                    إلغاء
                                </a>
                            </div>
                            
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                  current_user in project.managers or current_user.id == project.manager_id %}
                            <div>
                                <a href="{{ url_for('project.delete_timeline', timeline_id=timeline.id) }}" 
                                   class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>
                                    حذف التايم لاين
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-resize textarea
document.getElementById('description').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    
    if (!name) {
        e.preventDefault();
        alert('اسم التايم لاين مطلوب');
        document.getElementById('name').focus();
        return false;
    }
    
    if (name.length > 100) {
        e.preventDefault();
        alert('اسم التايم لاين يجب أن يكون أقل من 100 حرف');
        document.getElementById('name').focus();
        return false;
    }
});
</script>
{% endblock %}
