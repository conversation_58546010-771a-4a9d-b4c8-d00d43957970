{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus me-2"></i>
            إنشاء حساب جديد
        </h1>
        <a href="{{ url_for('accounting.accounts') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-right me-1"></i>العودة لشجرة الحسابات
        </a>
    </div>

    <!-- Create Account Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات الحساب الجديد</h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="createAccountForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="code" class="form-label">رمز الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="code" name="code" required 
                                       placeholder="مثال: 1110" maxlength="20">
                                <div class="form-text">رمز فريد للحساب (أرقام وحروف)</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم الحساب <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       placeholder="مثال: النقدية في الصندوق" maxlength="100">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="account_type" class="form-label">نوع الحساب <span class="text-danger">*</span></label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">-- اختر نوع الحساب --</option>
                                    {% for value, label in account_types %}
                                    <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="parent_id" class="form-label">الحساب الأب (اختياري)</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">-- حساب رئيسي --</option>
                                    {% for account in parent_accounts %}
                                    <option value="{{ account.id }}" 
                                            {% if request.args.get('parent_id') == account.id|string %}selected{% endif %}>
                                        {{ account.code }} - {{ account.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">اختر الحساب الأب إذا كان هذا حساب فرعي</div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الحساب</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="وصف مفصل للحساب وغرضه..."></textarea>
                        </div>

                        <!-- Account Type Info -->
                        <div class="alert alert-info" id="accountTypeInfo" style="display: none;">
                            <h6><i class="fas fa-info-circle me-2"></i>معلومات عن نوع الحساب</h6>
                            <p id="accountTypeDescription"></p>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-secondary" onclick="history.back()">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>إنشاء الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Account type descriptions
const accountTypeDescriptions = {
    'cash': 'النقدية: الأموال المتاحة في الصندوق أو الخزينة',
    'bank': 'البنوك: الحسابات البنكية والودائع',
    'receivables': 'المدينون: المبالغ المستحقة من العملاء',
    'assets': 'الأصول: الممتلكات والموارد التي تملكها الشركة',
    'fixed_assets': 'الأصول الثابتة: المعدات والمباني والأصول طويلة المدى',
    'inventory': 'المخزون: البضائع والمواد الخام',
    'liabilities': 'الخصوم: الالتزامات والديون على الشركة',
    'payables': 'الدائنون: المبالغ المستحقة للموردين',
    'equity': 'حقوق الملكية: رأس المال وحقوق المساهمين',
    'revenue': 'الإيرادات: الدخل من المبيعات والخدمات',
    'expenses': 'المصروفات: التكاليف والمصاريف التشغيلية',
    'other': 'أخرى: حسابات خاصة أو غير مصنفة'
};

// Show account type info when selection changes
document.getElementById('account_type').addEventListener('change', function() {
    const selectedType = this.value;
    const infoDiv = document.getElementById('accountTypeInfo');
    const descriptionP = document.getElementById('accountTypeDescription');
    
    if (selectedType && accountTypeDescriptions[selectedType]) {
        descriptionP.textContent = accountTypeDescriptions[selectedType];
        infoDiv.style.display = 'block';
    } else {
        infoDiv.style.display = 'none';
    }
});

// Auto-generate account code suggestion based on type and parent
document.getElementById('account_type').addEventListener('change', function() {
    const selectedType = this.value;
    const parentSelect = document.getElementById('parent_id');
    const codeInput = document.getElementById('code');
    
    // Only suggest if code is empty
    if (codeInput.value.trim() === '') {
        let suggestedCode = '';
        
        if (parentSelect.value) {
            // Get parent account code from the option text
            const parentOption = parentSelect.options[parentSelect.selectedIndex];
            const parentText = parentOption.textContent;
            const parentCode = parentText.split(' - ')[0];
            
            // Suggest next sub-account code
            if (parentCode) {
                const baseCode = parseInt(parentCode);
                suggestedCode = (baseCode + 10).toString();
            }
        } else {
            // Suggest main account codes based on type
            const typeCodeMap = {
                'cash': '1110',
                'bank': '1120',
                'receivables': '1130',
                'assets': '1000',
                'fixed_assets': '1200',
                'inventory': '1140',
                'liabilities': '2000',
                'payables': '2110',
                'equity': '3000',
                'revenue': '4000',
                'expenses': '5000',
                'other': '9000'
            };
            
            suggestedCode = typeCodeMap[selectedType] || '';
        }
        
        if (suggestedCode) {
            codeInput.value = suggestedCode;
            codeInput.style.backgroundColor = '#fff3cd';
            setTimeout(() => {
                codeInput.style.backgroundColor = '';
            }, 2000);
        }
    }
});

// Parent account change handler
document.getElementById('parent_id').addEventListener('change', function() {
    // Trigger account type change to update code suggestion
    document.getElementById('account_type').dispatchEvent(new Event('change'));
});

// Form validation
document.getElementById('createAccountForm').addEventListener('submit', function(e) {
    const code = document.getElementById('code').value.trim();
    const name = document.getElementById('name').value.trim();
    const accountType = document.getElementById('account_type').value;
    
    if (!code || !name || !accountType) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    // Validate code format (alphanumeric)
    const codeRegex = /^[a-zA-Z0-9]+$/;
    if (!codeRegex.test(code)) {
        e.preventDefault();
        alert('رمز الحساب يجب أن يحتوي على أرقام وحروف فقط');
        return false;
    }
    
    return true;
});

// Auto-focus on first input
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('code').focus();
});
</script>

<style>
.form-label {
    font-weight: 600;
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

.alert-info {
    border-left: 4px solid #17a2b8;
}

.card {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
}

.btn {
    border-radius: 0.35rem;
    font-weight: 600;
}

.form-control:focus,
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.text-danger {
    color: #e74a3b !important;
}
</style>
{% endblock %}
