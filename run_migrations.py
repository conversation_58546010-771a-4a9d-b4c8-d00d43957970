#!/usr/bin/env python3
"""
Script to run database migrations manually
"""

import os
import sys
from app import create_app, db
from sqlalchemy import text

def run_user_status_migration():
    """Add status fields to users table"""
    try:
        print("Running user status fields migration...")
        
        # Check if status column exists
        result = db.session.execute(text("PRAGMA table_info(users)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'status' not in columns:
            # Add status column
            db.session.execute(text("ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'offline'"))
            print("✓ Added status column to users table")
        else:
            print("✓ Status column already exists")
        
        if 'custom_status' not in columns:
            # Add custom_status column
            db.session.execute(text("ALTER TABLE users ADD COLUMN custom_status VARCHAR(255)"))
            print("✓ Added custom_status column to users table")
        else:
            print("✓ Custom_status column already exists")
        
        if 'last_activity' not in columns:
            # Add last_activity column (SQLite doesn't support CURRENT_TIMESTAMP as default in ALTER TABLE)
            db.session.execute(text("ALTER TABLE users ADD COLUMN last_activity DATETIME"))
            print("✓ Added last_activity column to users table")
        else:
            print("✓ Last_activity column already exists")
        
        # Update existing users to have default status
        db.session.execute(text("UPDATE users SET status = 'offline' WHERE status IS NULL"))
        db.session.execute(text("UPDATE users SET last_activity = CURRENT_TIMESTAMP WHERE last_activity IS NULL"))
        
        db.session.commit()
        print("✅ User status fields migration completed successfully")
        
    except Exception as e:
        print(f"❌ Error in user status fields migration: {e}")
        db.session.rollback()
        return False
    
    return True

def run_timeline_migration():
    """Create timeline tables"""
    try:
        print("Running timeline tables migration...")
        
        # Create project_timelines table
        db.session.execute(text("""
            CREATE TABLE IF NOT EXISTS project_timelines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                order_index INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                project_id INTEGER NOT NULL,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
        """))
        print("✓ Created project_timelines table")

        # Create timeline_steps table
        db.session.execute(text("""
            CREATE TABLE IF NOT EXISTS timeline_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                order_index INTEGER DEFAULT 0,
                source_type VARCHAR(20) NOT NULL,
                links TEXT,
                task_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                timeline_id INTEGER NOT NULL,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (timeline_id) REFERENCES project_timelines (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id),
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        """))
        print("✓ Created timeline_steps table")

        # Create indexes for better performance
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_project_timelines_project_id 
            ON project_timelines (project_id)
        """))
        
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_timeline_steps_timeline_id 
            ON timeline_steps (timeline_id)
        """))
        
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_timeline_steps_task_id 
            ON timeline_steps (task_id)
        """))
        
        db.session.commit()
        print("✅ Timeline tables migration completed successfully")
        
    except Exception as e:
        print(f"❌ Error in timeline tables migration: {e}")
        db.session.rollback()
        return False
    
    return True

def main():
    """Main function to run all migrations"""
    print("🚀 Starting database migrations...")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        # Run migrations
        success1 = run_user_status_migration()
        success2 = run_timeline_migration()
        
        if success1 and success2:
            print("\n🎉 All migrations completed successfully!")
            return True
        else:
            print("\n💥 Some migrations failed!")
            return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
