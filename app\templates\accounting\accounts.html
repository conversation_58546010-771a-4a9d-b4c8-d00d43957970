{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-sitemap me-2"></i>
            شجرة الحسابات
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ url_for('accounting.create_account') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>إنشاء حساب جديد
            </a>
            <a href="{{ url_for('accounting.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة للوحة التحكم
            </a>
        </div>
    </div>

    <!-- Accounts Tree -->
    <div class="card shadow">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">هيكل الحسابات</h6>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-primary" onclick="expandAll()">
                        <i class="fas fa-expand-arrows-alt me-1"></i>توسيع الكل
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="collapseAll()">
                        <i class="fas fa-compress-arrows-alt me-1"></i>طي الكل
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if accounts %}
            <div id="accounts-tree">
                {% for account in accounts %}
                    {{ render_account_tree(account) }}
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-sitemap fa-4x text-gray-300 mb-3"></i>
                <h5 class="text-gray-600">لا توجد حسابات</h5>
                <p class="text-muted">ابدأ بإنشاء حساب رئيسي جديد</p>
                <a href="{{ url_for('accounting.create_account') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>إنشاء حساب جديد
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Account Details Modal -->
<div class="modal fade" id="accountModal" tabindex="-1" aria-labelledby="accountModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="accountModalLabel">تفاصيل الحساب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="accountModalBody">
                <!-- Account details will be loaded here -->
            </div>
        </div>
    </div>
</div>

{% macro render_account_tree(account) %}
<div class="account-item" data-account-id="{{ account.id }}">
    <div class="account-header d-flex align-items-center justify-content-between p-2 border rounded mb-2">
        <div class="d-flex align-items-center">
            {% if account.has_children %}
            <button class="btn btn-sm btn-link p-0 me-2 toggle-btn" onclick="toggleAccount({{ account.id }})">
                <i class="fas fa-chevron-down"></i>
            </button>
            {% else %}
            <span class="me-4"></span>
            {% endif %}
            
            <div class="account-info">
                <div class="d-flex align-items-center">
                    <span class="badge bg-secondary me-2">{{ account.code }}</span>
                    <strong>{{ account.name }}</strong>
                    <span class="badge bg-info ms-2">{{ account.account_type }}</span>
                </div>
                {% if account.description %}
                <small class="text-muted">{{ account.description }}</small>
                {% endif %}
            </div>
        </div>
        
        <div class="account-actions d-flex align-items-center">
            <span class="balance me-3 {{ 'text-success' if account.balance >= 0 else 'text-danger' }}">
                <strong>{{ "%.2f"|format(account.balance) }} ر.س</strong>
            </span>
            
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-info" onclick="viewAccount({{ account.id }})">
                    <i class="fas fa-eye"></i>
                </button>
                <a href="{{ url_for('accounting.edit_account', account_id=account.id) }}" class="btn btn-outline-warning">
                    <i class="fas fa-edit"></i>
                </a>
                <a href="{{ url_for('accounting.create_account') }}?parent_id={{ account.id }}" class="btn btn-outline-success">
                    <i class="fas fa-plus"></i>
                </a>
                <button type="button" class="btn btn-outline-danger" onclick="deleteAccount({{ account.id }}, '{{ account.name }}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    </div>
    
    {% if account.children %}
    <div class="account-children ms-4" id="children-{{ account.id }}">
        {% for child in account.children %}
            {{ render_account_tree(child) }}
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endmacro %}

{% endblock %}

{% block scripts %}
<script>
function toggleAccount(accountId) {
    const childrenDiv = document.getElementById(`children-${accountId}`);
    const toggleBtn = document.querySelector(`[data-account-id="${accountId}"] .toggle-btn i`);
    
    if (childrenDiv.style.display === 'none') {
        childrenDiv.style.display = 'block';
        toggleBtn.className = 'fas fa-chevron-down';
    } else {
        childrenDiv.style.display = 'none';
        toggleBtn.className = 'fas fa-chevron-right';
    }
}

function expandAll() {
    document.querySelectorAll('.account-children').forEach(div => {
        div.style.display = 'block';
    });
    document.querySelectorAll('.toggle-btn i').forEach(icon => {
        icon.className = 'fas fa-chevron-down';
    });
}

function collapseAll() {
    document.querySelectorAll('.account-children').forEach(div => {
        div.style.display = 'none';
    });
    document.querySelectorAll('.toggle-btn i').forEach(icon => {
        icon.className = 'fas fa-chevron-right';
    });
}

function viewAccount(accountId) {
    fetch(`/accounting/accounts/${accountId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('accountModalBody').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('accountModal')).show();
            } else {
                alert('حدث خطأ أثناء تحميل تفاصيل الحساب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الحساب');
        });
}

function deleteAccount(accountId, accountName) {
    if (confirm(`هل أنت متأكد من حذف الحساب "${accountName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/accounting/accounts/${accountId}/delete`;
        
        // Add CSRF token if needed
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Initialize collapsed state for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Start with all children collapsed except first level
    document.querySelectorAll('.account-children').forEach((div, index) => {
        if (div.closest('.account-children')) {
            div.style.display = 'none';
            const accountId = div.id.replace('children-', '');
            const toggleBtn = document.querySelector(`[data-account-id="${accountId}"] .toggle-btn i`);
            if (toggleBtn) {
                toggleBtn.className = 'fas fa-chevron-right';
            }
        }
    });
});
</script>

<style>
.account-item {
    margin-bottom: 0.5rem;
}

.account-header {
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.account-header:hover {
    background-color: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toggle-btn {
    border: none !important;
    color: #6c757d;
    transition: transform 0.3s ease;
}

.toggle-btn:hover {
    color: #495057;
    transform: scale(1.1);
}

.account-children {
    border-left: 2px solid #dee2e6;
    padding-left: 1rem;
    margin-left: 1rem;
}

.balance {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}
</style>
{% endblock %}
