from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_file
from flask_login import login_required, current_user
from datetime import datetime, timezone, timedelta
import os
from werkzeug.utils import secure_filename

from app import db
from app.models.finance import Invoice, InvoiceItem, InvoiceAttachment
from app.models.client import Client
from app.models.project import Project
from app.models.department import Department
from app.models.user import User
from app.models.currency import Currency
finance_bp = Blueprint('finance', __name__, url_prefix='/finance')

@finance_bp.route('/')
@login_required
def index():
    # Check if user has permission to view finance
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get financial summary (updated for new accounting system)
    from app.models.accounting import Account, AccountType

    # Get revenue accounts (type revenue)
    revenue_accounts = Account.query.filter_by(account_type=AccountType.REVENUE).all()
    total_revenue = sum(float(account.balance) for account in revenue_accounts)

    # Get expense accounts (type expenses)
    expense_accounts = Account.query.filter_by(account_type=AccountType.EXPENSES).all()
    total_expenses = sum(float(account.balance) for account in expense_accounts)

    # Calcular el beneficio de la empresa (suma de company_profit de todas las facturas)
    total_company_profit = 0
    invoices = Invoice.query.all()
    for invoice in invoices:
        for item in invoice.items:
            total_company_profit += item.calculate_company_profit()

    # Calcular el beneficio general (ingresos + beneficio de la empresa)
    total_general_profit = total_revenue + total_company_profit

    # Calcular el beneficio neto (beneficio general total - gastos totales)
    net_profit = total_general_profit - total_expenses

    # Get pagination parameters for recent transactions
    transactions_page = request.args.get('transactions_page', 1, type=int)
    transactions_per_page = request.args.get('transactions_per_page', 5, type=int)
    transactions_type_filter = request.args.get('transactions_type', 'all')

    # Get recent transactions from new accounting system
    from app.models.finance import Transaction as NewTransaction

    try:
        # Build transactions query
        transactions_query = NewTransaction.query

        # Apply status filter to transactions (using new status field)
        if transactions_type_filter != 'all':
            if transactions_type_filter == 'income':
                # For income, we'll show paid transactions
                transactions_query = transactions_query.filter(NewTransaction.status == 'paid')
            elif transactions_type_filter == 'expense':
                # For expense, we'll show pending transactions
                transactions_query = transactions_query.filter(NewTransaction.status == 'pending')

        # Order transactions by date (newest first)
        transactions_query = transactions_query.order_by(NewTransaction.created_at.desc())

        # Paginate transactions
        recent_transactions = transactions_query.paginate(page=transactions_page, per_page=transactions_per_page, error_out=False)
    except:
        # If there's an error, create empty pagination
        from flask_sqlalchemy import Pagination
        recent_transactions = Pagination(query=None, page=1, per_page=5, total=0, items=[])

    # Get pagination parameters for recent invoices
    invoices_page = request.args.get('invoices_page', 1, type=int)
    invoices_per_page = request.args.get('invoices_per_page', 5, type=int)
    invoices_status_filter = request.args.get('invoices_status', 'all')

    # Build invoices query
    invoices_query = Invoice.query

    # Apply status filter to invoices
    if invoices_status_filter != 'all':
        invoices_query = invoices_query.filter(Invoice.status == invoices_status_filter)

    # Order invoices by issue date (newest first)
    invoices_query = invoices_query.order_by(Invoice.issue_date.desc())

    # Paginate invoices
    recent_invoices = invoices_query.paginate(page=invoices_page, per_page=invoices_per_page, error_out=False)

    # Get pending invoices count
    pending_invoices_count = Invoice.query.filter_by(status='pending').count()

    # Get real data for charts
    import calendar
    from collections import defaultdict
    from datetime import datetime, timedelta

    # Get current year
    current_year = datetime.now().year

    # Initialize monthly data
    monthly_general_profit = [0] * 12
    monthly_expenses = [0] * 12

    # Get transactions for current year
    year_start = datetime(current_year, 1, 1)
    year_end = datetime(current_year, 12, 31, 23, 59, 59)

    # Use empty list for now since we're transitioning to new system
    year_transactions = []

    # Get invoices for current year
    year_invoices = Invoice.query.filter(
        Invoice.issue_date >= year_start,
        Invoice.issue_date <= year_end
    ).all()

    # Aggregate transactions by month
    for transaction in year_transactions:
        month_idx = transaction.date.month - 1  # 0-based index
        if transaction.transaction_type == 'income':
            monthly_general_profit[month_idx] += transaction.amount
        else:  # expense
            monthly_expenses[month_idx] += transaction.amount

    # Add company profit from invoices by month
    for invoice in year_invoices:
        month_idx = invoice.issue_date.month - 1  # 0-based index
        for item in invoice.items:
            monthly_general_profit[month_idx] += item.calculate_company_profit()

    # Get expense categories
    expense_categories_dict = defaultdict(float)
    for transaction in expenses:
        category = transaction.category or 'أخرى'
        expense_categories_dict[category] += transaction.amount

    # Convert to lists for the template
    expense_categories = list(expense_categories_dict.keys())
    expense_amounts = list(expense_categories_dict.values())

    # If no expense categories, provide defaults
    if not expense_categories:
        expense_categories = ['أخرى']
        expense_amounts = [0]

    return render_template('finance/index.html', title='Finance Dashboard',
                          total_revenue=total_revenue, total_expenses=total_expenses, net_profit=net_profit,
                          total_company_profit=total_company_profit, total_general_profit=total_general_profit,
                          recent_transactions=recent_transactions, recent_invoices=recent_invoices,
                          pending_invoices_count=pending_invoices_count,
                          monthly_income=monthly_general_profit, monthly_expenses=monthly_expenses,
                          expense_categories=expense_categories, expense_amounts=expense_amounts,
                          # Transaction pagination and filtering parameters
                          transactions_type_filter=transactions_type_filter,
                          transactions_per_page=transactions_per_page,
                          # Invoice pagination and filtering parameters
                          invoices_status_filter=invoices_status_filter,
                          invoices_per_page=invoices_per_page)

@finance_bp.route('/transactions')
@login_required
def transactions():
    # Redirect to new transactions system
    flash('تم نقل نظام المعاملات إلى النظام المحاسبي الجديد', 'info')
    return redirect(url_for('transactions.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    transaction_type = request.args.get('type', '')
    category = request.args.get('category', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    project_id = request.args.get('project_id', '')

    # Build the query
    query = Transaction.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Transaction.description.like(search_term)) |
            (Transaction.category.like(search_term))
        )

    # Apply transaction type filter if provided
    if transaction_type and transaction_type != 'all':
        query = query.filter(Transaction.transaction_type == transaction_type)

    # Apply category filter if provided
    if category and category != 'all':
        query = query.filter(Transaction.category == category)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Transaction.date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Transaction.date <= to_date)
        except ValueError:
            pass

    # Apply project filter if provided
    if project_id:
        query = query.filter(Transaction.project_id == project_id)

    # Order by date (newest first)
    query = query.order_by(Transaction.date.desc())

    # Paginate the results
    transactions = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get projects for filter and form
    projects = Project.query.all()

    # Get unique categories for filter
    categories = db.session.query(Transaction.category).distinct().all()
    categories = [c[0] for c in categories if c[0]]

    return render_template('finance/transactions.html',
                          title='Transactions',
                          transactions=transactions,
                          projects=projects,
                          categories=categories,
                          search_query=search_query,
                          transaction_type=transaction_type,
                          category=category,
                          date_from=date_from,
                          date_to=date_to,
                          project_id=project_id,
                          current_per_page=per_page)

@finance_bp.route('/add_transaction', methods=['GET', 'POST'])
@login_required
def add_transaction():
    # Check if user has permission to add transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إضافة معاملات مالية', 'danger')
        return redirect(url_for('finance.transactions'))

    # Get projects for the form
    projects = Project.query.all()

    # Get users for supervisor selection
    users = User.query.all()

    # Get currencies for the form
    currencies = Currency.query.all()

    # Get today's date for default issue date
    today = datetime.now().strftime('%Y-%m-%d')

    if request.method == 'POST':
        amount = float(request.form.get('amount'))
        transaction_type = request.form.get('type')  # Changed from transaction_type to type
        category = request.form.get('category')
        description = request.form.get('description')
        date_str = request.form.get('date')
        project_id = request.form.get('project_id')
        notes = request.form.get('notes')

        # Get new fields
        supervisor_id = request.form.get('supervisor_id')
        recipient = request.form.get('recipient')

        # Get alternative currency fields
        alt_amount = request.form.get('alt_amount')
        currency_id = request.form.get('currency_id')

        # Parse date
        date = datetime.strptime(date_str, '%Y-%m-%d') if date_str else datetime.now(timezone.utc)

        # Create new transaction
        transaction = Transaction(
            amount=amount,
            transaction_type=transaction_type,
            category=category,
            description=description,
            date=date,
            recorded_by_id=current_user.id,
            supervisor_id=supervisor_id if supervisor_id else None,
            recipient=recipient,
            alt_amount=float(alt_amount) if alt_amount else None,
            currency_id=currency_id if currency_id else None
        )

        # Set project if provided
        if project_id:
            transaction.project_id = project_id

        db.session.add(transaction)
        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'transactions', str(transaction.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Save file information to database
                    file_attachment = TransactionAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'transactions', str(transaction.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        transaction_id=transaction.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')

            for i in range(len(links)):
                if links[i].strip():  # Only add if URL is not empty
                    link = TransactionVerificationLink(
                        url=links[i],
                        description=descriptions[i] if i < len(descriptions) else None,
                        transaction_id=transaction.id,
                        added_by_id=current_user.id
                    )
                    db.session.add(link)

        db.session.commit()

        flash('تمت إضافة المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/add_transaction.html', title='إضافة معاملة جديدة',
                          projects=projects, users=users, currencies=currencies, today=today)

@finance_bp.route('/edit_transaction/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_transaction(id):
    # Check if user has permission to edit transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المعاملات المالية', 'danger')
        return redirect(url_for('finance.transactions'))

    transaction = Transaction.query.get_or_404(id)

    # Get projects for the form
    projects = Project.query.all()

    # Get users for supervisor selection
    users = User.query.all()

    # Get currencies for the form
    currencies = Currency.query.all()

    if request.method == 'POST':
        transaction.amount = float(request.form.get('amount'))
        transaction.transaction_type = request.form.get('type')  # Changed from transaction_type to type
        transaction.category = request.form.get('category')
        transaction.description = request.form.get('description')
        project_id = request.form.get('project_id')
        notes = request.form.get('notes')

        # Get new fields
        supervisor_id = request.form.get('supervisor_id')
        recipient = request.form.get('recipient')

        # Get alternative currency fields
        alt_amount = request.form.get('alt_amount')
        currency_id = request.form.get('currency_id')

        # Parse date
        date_str = request.form.get('date')
        if date_str:
            transaction.date = datetime.strptime(date_str, '%Y-%m-%d')

        # Set project if provided
        if project_id:
            transaction.project_id = project_id
        else:
            transaction.project_id = None

        # Set notes if provided
        if notes:
            transaction.notes = notes

        # Set new fields
        transaction.supervisor_id = supervisor_id if supervisor_id else None
        transaction.recipient = recipient
        transaction.alt_amount = float(alt_amount) if alt_amount else None
        transaction.currency_id = currency_id if currency_id else None

        db.session.commit()

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'transactions', str(transaction.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Save file information to database
                    file_attachment = TransactionAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'transactions', str(transaction.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        transaction_id=transaction.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            # First, get existing link IDs to determine which ones to keep
            existing_link_ids = set()
            if 'verification_link_ids[]' in request.form:
                existing_link_ids = set(map(int, request.form.getlist('verification_link_ids[]')))

            # Delete links that are not in the form
            for link in transaction.verification_links.all():
                if link.id not in existing_link_ids:
                    db.session.delete(link)

            # Update or add links
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')
            link_ids = request.form.getlist('verification_link_ids[]') if 'verification_link_ids[]' in request.form else []

            for i in range(len(links)):
                if links[i].strip():  # Only process if URL is not empty
                    if i < len(link_ids) and link_ids[i]:
                        # Update existing link
                        link_id = int(link_ids[i])
                        link = TransactionVerificationLink.query.get(link_id)
                        if link and link.transaction_id == transaction.id:
                            link.url = links[i]
                            link.description = descriptions[i] if i < len(descriptions) else None
                    else:
                        # Add new link
                        link = TransactionVerificationLink(
                            url=links[i],
                            description=descriptions[i] if i < len(descriptions) else None,
                            transaction_id=transaction.id,
                            added_by_id=current_user.id
                        )
                        db.session.add(link)

        db.session.commit()

        flash('تم تحديث المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/edit_transaction.html', title='تعديل المعاملة',
                          transaction=transaction, projects=projects, users=users, currencies=currencies)

@finance_bp.route('/delete_transaction/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_transaction(id):
    # Only admin can delete transactions
    if not current_user.has_role('admin'):
        flash('لا تملك صلاحية حذف المعاملات المالية', 'danger')
        return redirect(url_for('finance.transactions'))

    transaction = Transaction.query.get_or_404(id)

    if request.method == 'POST':
        # Delete attachments from filesystem
        for attachment in transaction.attachments:
            file_path = os.path.join('app', 'static', attachment.file_path)
            try:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {str(e)}")
                # Continue with deletion even if file removal fails

        db.session.delete(transaction)
        db.session.commit()

        flash('تم حذف المعاملة بنجاح', 'success')
        return redirect(url_for('finance.transactions'))

    return render_template('finance/delete_transaction.html', title='حذف المعاملة', transaction=transaction)

@finance_bp.route('/invoices')
@login_required
def invoices():
    # Check if user has permission to view invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    client_id = request.args.get('client_id', '')
    project_id = request.args.get('project_id', '')

    # Build the query
    query = Invoice.query

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Invoice.invoice_number.like(search_term)) |
            (Invoice.notes.like(search_term))
        )

    # Apply status filter if provided
    if status_filter and status_filter != 'all':
        query = query.filter(Invoice.status == status_filter)

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Invoice.issue_date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Invoice.issue_date <= to_date)
        except ValueError:
            pass

    # Apply client filter if provided
    if client_id:
        query = query.filter(Invoice.client_id == client_id)

    # Apply project filter if provided
    if project_id:
        query = query.filter(Invoice.project_id == project_id)

    # Order by issue date (newest first)
    query = query.order_by(Invoice.issue_date.desc())

    # Paginate the results
    invoices = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get clients and projects for filters and modal
    clients = Client.query.all()
    projects = Project.query.all()

    return render_template('finance/invoices.html',
                          title='Invoices',
                          invoices=invoices,
                          clients=clients,
                          projects=projects,
                          search_query=search_query,
                          status_filter=status_filter,
                          date_from=date_from,
                          date_to=date_to,
                          client_id=client_id,
                          project_id=project_id,
                          current_per_page=per_page)

@finance_bp.route('/add_invoice', methods=['GET', 'POST'])
@login_required
def add_invoice():
    # Check if user has permission to create invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إنشاء فواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get clients, projects and employees for the form
    clients = Client.query.all()
    projects = Project.query.all()
    employees = User.query.all()

    # For GET request, show the create invoice form
    if request.method == 'GET':
        return render_template('finance/create_invoice.html', title='إنشاء فاتورة جديدة',
                              clients=clients, projects=projects, employees=employees,
                              now=datetime.now(timezone.utc))

    if request.method == 'POST':
        invoice_number = request.form.get('invoice_number')
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')

        # Fechas
        issue_date_str = request.form.get('issue_date')
        due_date_str = request.form.get('due_date')
        approval_date_str = request.form.get('approval_date')

        # Comisiones, impuestos y descuentos
        transfer_fee_type = request.form.get('transfer_fee_type', 'percentage')
        transfer_fee_value = float(request.form.get('transfer_fee_value', 0))
        tax_type = request.form.get('tax_type', 'percentage')
        tax_value = float(request.form.get('tax_value', 0))
        discount_type = request.form.get('discount_type', 'percentage')
        discount_value = float(request.form.get('discount_value', 0))

        status = request.form.get('status')
        notes = request.form.get('notes')

        # Parse dates
        issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d') if issue_date_str else datetime.now(timezone.utc)
        due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None
        approval_date = datetime.strptime(approval_date_str, '%Y-%m-%d') if approval_date_str else None

        # Create new invoice
        invoice = Invoice(
            invoice_number=invoice_number,
            issue_date=issue_date,
            due_date=due_date,
            approval_date=approval_date,
            transfer_fee_type=transfer_fee_type,
            transfer_fee_value=transfer_fee_value,
            tax_type=tax_type,
            tax_value=tax_value,
            discount_type=discount_type,
            discount_value=discount_value,
            status=status,
            notes=notes,
            client_id=client_id,
            project_id=project_id if project_id else None,
            created_by_id=current_user.id
        )

        db.session.add(invoice)
        db.session.commit()

        # Procesar elementos de línea de factura
        item_descriptions = request.form.getlist('item_description[]')
        item_quantities = request.form.getlist('item_quantity[]')
        item_unit_prices = request.form.getlist('item_unit_price[]')
        item_supervisors = request.form.getlist('item_supervisor[]')
        item_company_profit_types = request.form.getlist('item_company_profit_type[]')
        item_company_profit_values = request.form.getlist('item_company_profit_value[]')

        item_statuses = request.form.getlist('item_status[]')
        item_receipt_dates = request.form.getlist('item_receipt_date[]')

        for i in range(len(item_descriptions)):
            if item_descriptions[i].strip():  # Solo agregar si hay descripción
                # Procesar fecha de recepción si existe
                receipt_date = None
                if i < len(item_receipt_dates) and item_receipt_dates[i]:
                    receipt_date = datetime.strptime(item_receipt_dates[i], '%Y-%m-%d')

                item = InvoiceItem(
                    description=item_descriptions[i],
                    quantity=float(item_quantities[i]) if item_quantities[i] else 1,
                    unit_price=float(item_unit_prices[i]) if item_unit_prices[i] else 0,
                    supervisor_id=int(item_supervisors[i]) if item_supervisors[i] else None,
                    company_profit_type=item_company_profit_types[i] if item_company_profit_types[i] else 'percentage',
                    company_profit_value=float(item_company_profit_values[i]) if item_company_profit_values[i] else 0,
                    status=item_statuses[i] if i < len(item_statuses) else 'غير مستلم',
                    receipt_date=receipt_date,
                    invoice_id=invoice.id
                )
                db.session.add(item)

        # Procesar archivos adjuntos
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Crear directorio para archivos si no existe
            upload_dir = os.path.join('app', 'static', 'uploads', 'invoices', str(invoice.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Guardar información del archivo en la base de datos
                    file_attachment = InvoiceAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'invoices', str(invoice.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        invoice_id=invoice.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')

            for i in range(len(links)):
                if links[i].strip():  # Only add if URL is not empty
                    link = InvoiceVerificationLink(
                        url=links[i],
                        description=descriptions[i] if i < len(descriptions) else None,
                        invoice_id=invoice.id,
                        added_by_id=current_user.id
                    )
                    db.session.add(link)

        # Actualizar totales
        invoice.update_total()
        db.session.commit()

        flash('تم إنشاء الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.view_invoice', id=invoice.id))

    return redirect(url_for('finance.invoices'))

@finance_bp.route('/view_invoice/<int:id>')
@login_required
def view_invoice(id):
    # Check if user has permission to view invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    invoice = Invoice.query.get_or_404(id)
    clients = Client.query.all()
    projects = Project.query.all()

    # Calcular totales
    subtotal = invoice.calculate_subtotal()
    transfer_fee = invoice.calculate_transfer_fee()
    tax = invoice.calculate_tax()
    discount = invoice.calculate_discount()
    total = invoice.calculate_total()

    # Calcular beneficio de la empresa
    company_profit = sum(item.calculate_company_profit() for item in invoice.items)

    return render_template('finance/view_invoice.html', title=f'Invoice: {invoice.invoice_number}',
                          invoice=invoice, clients=clients, projects=projects,
                          subtotal=subtotal, transfer_fee=transfer_fee, tax=tax,
                          discount=discount, total=total, company_profit=company_profit)

@finance_bp.route('/edit_invoice/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_invoice(id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    # Only admin can edit paid invoices
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.invoices'))

    # Get clients, projects and employees for the form
    clients = Client.query.all()
    projects = Project.query.all()
    employees = User.query.all()

    if request.method == 'POST':
        invoice_number = request.form.get('invoice_number')
        client_id = request.form.get('client_id')
        project_id = request.form.get('project_id')

        # Fechas
        issue_date_str = request.form.get('issue_date')
        due_date_str = request.form.get('due_date')
        approval_date_str = request.form.get('approval_date')

        # Comisiones, impuestos y descuentos
        transfer_fee_type = request.form.get('transfer_fee_type', 'percentage')
        transfer_fee_value = float(request.form.get('transfer_fee_value', 0))
        tax_type = request.form.get('tax_type', 'percentage')
        tax_value = float(request.form.get('tax_value', 0))
        discount_type = request.form.get('discount_type', 'percentage')
        discount_value = float(request.form.get('discount_value', 0))

        status = request.form.get('status')
        notes = request.form.get('notes')

        # Update invoice
        invoice.invoice_number = invoice_number
        invoice.client_id = client_id
        invoice.project_id = project_id if project_id else None
        invoice.transfer_fee_type = transfer_fee_type
        invoice.transfer_fee_value = transfer_fee_value
        invoice.tax_type = tax_type
        invoice.tax_value = tax_value
        invoice.discount_type = discount_type
        invoice.discount_value = discount_value
        invoice.status = status
        invoice.notes = notes

        # Parse dates
        if issue_date_str:
            invoice.issue_date = datetime.strptime(issue_date_str, '%Y-%m-%d')

        if due_date_str:
            invoice.due_date = datetime.strptime(due_date_str, '%Y-%m-%d')
        else:
            invoice.due_date = None

        if approval_date_str:
            invoice.approval_date = datetime.strptime(approval_date_str, '%Y-%m-%d')
        else:
            invoice.approval_date = None

        # Procesar elementos de línea de factura
        # Primero, eliminar los elementos existentes
        for item in invoice.items.all():
            db.session.delete(item)

        # Luego, agregar los nuevos elementos
        item_descriptions = request.form.getlist('item_description[]')
        item_quantities = request.form.getlist('item_quantity[]')
        item_unit_prices = request.form.getlist('item_unit_price[]')
        item_supervisors = request.form.getlist('item_supervisor[]')
        item_company_profit_types = request.form.getlist('item_company_profit_type[]')
        item_company_profit_values = request.form.getlist('item_company_profit_value[]')

        item_statuses = request.form.getlist('item_status[]')
        item_receipt_dates = request.form.getlist('item_receipt_date[]')

        for i in range(len(item_descriptions)):
            if item_descriptions[i].strip():  # Solo agregar si hay descripción
                # Procesar fecha de recepción si existe
                receipt_date = None
                if i < len(item_receipt_dates) and item_receipt_dates[i]:
                    receipt_date = datetime.strptime(item_receipt_dates[i], '%Y-%m-%d')

                item = InvoiceItem(
                    description=item_descriptions[i],
                    quantity=float(item_quantities[i]) if item_quantities[i] else 1,
                    unit_price=float(item_unit_prices[i]) if item_unit_prices[i] else 0,
                    supervisor_id=int(item_supervisors[i]) if item_supervisors[i] else None,
                    company_profit_type=item_company_profit_types[i] if item_company_profit_types[i] else 'percentage',
                    company_profit_value=float(item_company_profit_values[i]) if item_company_profit_values[i] else 0,
                    status=item_statuses[i] if i < len(item_statuses) else 'غير مستلم',
                    receipt_date=receipt_date,
                    invoice_id=invoice.id
                )
                db.session.add(item)

        # Procesar archivos adjuntos
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Crear directorio para archivos si no existe
            upload_dir = os.path.join('app', 'static', 'uploads', 'invoices', str(invoice.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Obtener el tamaño del archivo
                    file_size = os.path.getsize(file_path)

                    # Guardar información del archivo en la base de datos
                    file_attachment = InvoiceAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'invoices', str(invoice.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=file_size,
                        invoice_id=invoice.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        # Process verification links
        if 'verification_links[]' in request.form:
            # First, get existing link IDs to determine which ones to keep
            existing_link_ids = set()
            if 'verification_link_ids[]' in request.form:
                existing_link_ids = set(map(int, request.form.getlist('verification_link_ids[]')))

            # Delete links that are not in the form
            for link in invoice.verification_links.all():
                if link.id not in existing_link_ids:
                    db.session.delete(link)

            # Update or add links
            links = request.form.getlist('verification_links[]')
            descriptions = request.form.getlist('verification_descriptions[]')
            link_ids = request.form.getlist('verification_link_ids[]') if 'verification_link_ids[]' in request.form else []

            for i in range(len(links)):
                if links[i].strip():  # Only process if URL is not empty
                    if i < len(link_ids) and link_ids[i]:
                        # Update existing link
                        link_id = int(link_ids[i])
                        link = InvoiceVerificationLink.query.get(link_id)
                        if link and link.invoice_id == invoice.id:
                            link.url = links[i]
                            link.description = descriptions[i] if i < len(descriptions) else None
                    else:
                        # Add new link
                        link = InvoiceVerificationLink(
                            url=links[i],
                            description=descriptions[i] if i < len(descriptions) else None,
                            invoice_id=invoice.id,
                            added_by_id=current_user.id
                        )
                        db.session.add(link)

        # Actualizar totales
        invoice.update_total()
        db.session.commit()

        flash('تم تحديث الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.view_invoice', id=invoice.id))

    return render_template('finance/edit_invoice.html', title=f'تعديل الفاتورة: {invoice.invoice_number}',
                          invoice=invoice, clients=clients, projects=projects, employees=employees)

@finance_bp.route('/delete_invoice/<int:id>', methods=['GET', 'POST'])
@login_required
def delete_invoice(id):
    # Only admin or finance can delete invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    if request.method == 'POST':
        # Only admin can delete paid invoices
        if invoice.status == 'paid' and not current_user.has_role('admin'):
            flash('لا يمكن حذف الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
            return redirect(url_for('finance.view_invoice', id=id))

        # Delete attachments from filesystem
        for attachment in invoice.attachments:
            file_path = os.path.join('app', 'static', attachment.file_path)
            try:
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    os.remove(file_path)
            except Exception as e:
                print(f"Error deleting file {file_path}: {str(e)}")
                # Continue with deletion even if file removal fails

        db.session.delete(invoice)
        db.session.commit()

        flash('تم حذف الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.invoices'))

    return render_template('finance/delete_invoice.html', title='حذف الفاتورة', invoice=invoice)

@finance_bp.route('/add_invoice_item/<int:invoice_id>', methods=['GET', 'POST'])
@login_required
def add_invoice_item(invoice_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    # Get employees for supervisor selection
    employees = User.query.all()

    if request.method == 'POST':
        description = request.form.get('description')
        quantity = float(request.form.get('quantity'))
        unit_price = float(request.form.get('unit_price'))
        supervisor_id = request.form.get('supervisor_id')
        company_profit_type = request.form.get('company_profit_type', 'percentage')
        company_profit_value = float(request.form.get('company_profit_value', 0))
        status = request.form.get('status', 'غير مستلم')
        receipt_date_str = request.form.get('receipt_date')

        # Procesar fecha de recepción si existe
        receipt_date = None
        if receipt_date_str:
            receipt_date = datetime.strptime(receipt_date_str, '%Y-%m-%d')

        # Create new invoice item
        item = InvoiceItem(
            description=description,
            quantity=quantity,
            unit_price=unit_price,
            supervisor_id=supervisor_id if supervisor_id else None,
            company_profit_type=company_profit_type,
            company_profit_value=company_profit_value,
            status=status,
            receipt_date=receipt_date,
            invoice_id=invoice_id
        )

        db.session.add(item)

        # Update invoice total
        invoice.update_total()
        db.session.commit()

        flash('تم إضافة عنصر الفاتورة بنجاح', 'success')
        return redirect(url_for('finance.edit_invoice', id=invoice_id))

    return render_template('finance/add_invoice_item.html', title=f'إضافة عنصر للفاتورة: {invoice.invoice_number}',
                          invoice=invoice, employees=employees)

@finance_bp.route('/delete_invoice_item/<int:item_id>', methods=['POST'])
@login_required
def delete_invoice_item(item_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    item = InvoiceItem.query.get_or_404(item_id)
    invoice_id = item.invoice_id
    invoice = Invoice.query.get(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    db.session.delete(item)

    # Update invoice total
    invoice.update_total()
    db.session.commit()

    flash('تم حذف عنصر الفاتورة بنجاح', 'success')
    return redirect(url_for('finance.edit_invoice', id=invoice_id))

@finance_bp.route('/delete_invoice_attachment/<int:attachment_id>', methods=['POST'])
@login_required
def delete_invoice_attachment(attachment_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get the attachment with a fresh query to ensure it's attached to the session
    attachment = InvoiceAttachment.query.get_or_404(attachment_id)

    # Store invoice_id before deleting the attachment
    invoice_id = attachment.invoice_id

    # Get the invoice with a fresh query
    invoice = Invoice.query.get_or_404(invoice_id)

    # Cannot edit paid invoices unless admin
    if invoice.status == 'paid' and not current_user.has_role('admin'):
        flash('لا يمكن تعديل الفواتير المدفوعة إلا بواسطة المسؤول', 'warning')
        return redirect(url_for('finance.view_invoice', id=invoice_id))

    # Get file path before deleting from database
    file_path = os.path.join('app', 'static', attachment.file_path)

    try:
        # Delete from database first
        db.session.delete(attachment)
        db.session.commit()

        # Then delete file from disk if it exists
        if os.path.exists(file_path):
            os.remove(file_path)

        flash('تم حذف المرفق بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المرفق: {str(e)}', 'danger')

    return redirect(url_for('finance.edit_invoice', id=invoice_id))

@finance_bp.route('/mark_invoice_paid/<int:id>', methods=['POST'])
@login_required
def mark_invoice_paid(id):
    # Check if user has permission to manage invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية إدارة الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    invoice = Invoice.query.get_or_404(id)

    # Already paid
    if invoice.status == 'paid':
        flash('الفاتورة مدفوعة بالفعل', 'info')
        return redirect(url_for('finance.view_invoice', id=id))

    # Mark as paid and create transaction
    invoice.mark_as_paid()
    db.session.commit()

    flash('تم تحديد الفاتورة كمدفوعة بنجاح', 'success')
    return redirect(url_for('finance.view_invoice', id=id))

@finance_bp.route('/reports')
@login_required
def reports():
    # Check if user has permission to view reports
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية الوصول إلى هذه الصفحة', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get clients and departments for filters
    clients = Client.query.all()
    departments = Department.query.all()

    # Get expense categories for filters
    categories = set()
    for transaction in Transaction.query.all():
        if transaction.category:
            categories.add(transaction.category)

    return render_template('finance/reports.html', title='Financial Reports',
                          clients=clients, departments=departments, categories=list(categories))


@finance_bp.route('/view_transaction/<int:transaction_id>')
@login_required
def view_transaction(transaction_id):
    # Get the transaction
    transaction = Transaction.query.get_or_404(transaction_id)

    return render_template('finance/view_transaction.html', title='عرض المعاملة', transaction=transaction)


@finance_bp.route('/attachments')
@login_required
def attachments():
    # Check if user has permission to view attachments
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية عرض المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get all attachments
    attachments = TransactionAttachment.query.order_by(TransactionAttachment.uploaded_at.desc()).all()
    return render_template('finance/attachments.html', title='إدارة المرفقات', attachments=attachments)


@finance_bp.route('/download_attachment/<int:attachment_id>')
@login_required
def download_attachment(attachment_id):
    # Get the attachment
    attachment = TransactionAttachment.query.get_or_404(attachment_id)

    # Check if user has permission to download the attachment
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تنزيل المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('finance.attachments'))

    # Return the file as an attachment
    return send_file(file_path, as_attachment=True, download_name=attachment.filename)


@finance_bp.route('/download_invoice_attachment/<int:attachment_id>')
@login_required
def download_invoice_attachment(attachment_id):
    # Get the attachment
    attachment = InvoiceAttachment.query.get_or_404(attachment_id)

    # Check if user has permission to download the attachment
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تنزيل المرفقات', 'danger')
        return redirect(url_for('finance.index'))

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Check if file exists
    if not os.path.exists(file_path):
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('finance.invoices'))

    # Return the file as an attachment
    return send_file(file_path, as_attachment=True, download_name=attachment.filename)


@finance_bp.route('/delete_attachment/<int:attachment_id>')
@login_required
def delete_attachment(attachment_id):
    # Check if user has permission to delete attachments
    if not (current_user.has_role('admin') or current_user.has_role('finance')):
        flash('لا تملك صلاحية حذف المرفقات', 'danger')
        return redirect(url_for('finance.attachments'))

    # Get the attachment
    attachment = TransactionAttachment.query.get_or_404(attachment_id)

    # Get the file path
    file_path = os.path.join('app', 'static', attachment.file_path)

    # Delete the file if it exists
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete the attachment from the database
    db.session.delete(attachment)
    db.session.commit()

    flash('تم حذف المرفق بنجاح', 'success')
    return redirect(url_for('finance.attachments'))


@finance_bp.route('/delete_transaction_verification_link/<int:link_id>', methods=['GET', 'POST'])
@login_required
def delete_transaction_verification_link(link_id):
    # Check if user has permission to edit transactions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل المعاملات', 'danger')
        return redirect(url_for('finance.transactions'))

    # Get the link
    link = TransactionVerificationLink.query.get_or_404(link_id)
    transaction_id = link.transaction_id

    # Delete the link
    db.session.delete(link)
    db.session.commit()

    flash('تم حذف رابط الإثبات بنجاح', 'success')
    return redirect(url_for('finance.view_transaction', transaction_id=transaction_id))


@finance_bp.route('/delete_invoice_verification_link/<int:link_id>', methods=['GET', 'POST'])
@login_required
def delete_invoice_verification_link(link_id):
    # Check if user has permission to edit invoices
    if not (current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance')):
        flash('لا تملك صلاحية تعديل الفواتير', 'danger')
        return redirect(url_for('finance.invoices'))

    # Get the link
    link = InvoiceVerificationLink.query.get_or_404(link_id)
    invoice_id = link.invoice_id

    # Delete the link
    db.session.delete(link)
    db.session.commit()

    flash('تم حذف رابط الإثبات بنجاح', 'success')
    return redirect(url_for('finance.view_invoice', id=invoice_id))
