from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, current_user, login_required
from urllib.parse import urlparse
from datetime import datetime
import time

from app import db
from app.models.user import User, Role
from app.models.system_config import SystemConfig
from app.utils.activity_logger import log_activity
from app.utils.security import is_safe_redirect_url, generate_csrf_token, validate_csrf_token, rate_limit_ip

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    # Check if system is in maintenance mode
    maintenance_mode = SystemConfig.is_maintenance_mode()

    # Generate CSRF token for the form
    csrf_token = generate_csrf_token()

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember = 'remember' in request.form

        user = User.query.filter_by(username=username).first()

        if user is None or not user.check_password(password):
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
            # Log failed login attempt
            log_activity(
                action='login_failed',
                entity_type='auth',
                entity_id=0,
                description=f'محاولة تسجيل دخول فاشلة: {username}'
            )
            return redirect(url_for('auth.login'))

        if not user.is_active:
            flash('تم تعطيل هذا الحساب. يرجى الاتصال بالمسؤول.', 'warning')
            return redirect(url_for('auth.login'))

        # If system is in maintenance mode and user is not admin, deny login
        if maintenance_mode and not user.has_role('admin'):
            flash('النظام في وضع الصيانة حالياً. فقط المسؤولون يمكنهم تسجيل الدخول.', 'warning')
            return redirect(url_for('auth.login'))

        login_user(user, remember=remember)
        user.last_login = datetime.now().replace(microsecond=0)

        # Update user status to online
        user.set_status('online')

        db.session.commit()

        # Log the login activity
        log_activity(
            action='login',
            entity_type='user',
            entity_id=user.id,
            description=f'تسجيل دخول المستخدم: {user.username}'
        )

        next_page = request.args.get('next')
        if not next_page or urlparse(next_page).netloc != '':
            next_page = url_for('dashboard.index')

        return redirect(next_page)

    return render_template('auth/login.html', title='تسجيل الدخول',
                          maintenance_mode=maintenance_mode, csrf_token=csrf_token)

@auth_bp.route('/logout')
def logout():
    if current_user.is_authenticated:
        user_id = current_user.id
        username = current_user.username

        # Update user status to offline before logout
        current_user.set_status('offline')
        db.session.commit()

        logout_user()

        # Log the logout activity
        log_activity(
            action='logout',
            entity_type='user',
            entity_id=user_id,
            description=f'تسجيل خروج المستخدم: {username}'
        )
    else:
        logout_user()

    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return redirect(url_for('auth.register'))

        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'danger')
            return redirect(url_for('auth.register'))

        if User.query.filter_by(email=email).first():
            flash('Email already exists', 'danger')
            return redirect(url_for('auth.register'))

        user = User(username=username, email=email)
        user.set_password(password)

        # Assign default role (employee)
        employee_role = Role.query.filter_by(name='employee').first()
        if employee_role:
            user.roles.append(employee_role)

        db.session.add(user)
        db.session.commit()

        flash('Registration successful! You can now log in.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/register.html', title='Register')

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    if request.method == 'POST':
        current_user.first_name = request.form.get('first_name')
        current_user.last_name = request.form.get('last_name')
        current_user.phone = request.form.get('phone')
        current_user.bio = request.form.get('bio')

        # Handle profile image upload
        if 'profile_image' in request.files and request.files['profile_image'].filename:
            from werkzeug.utils import secure_filename
            import os
            from app.config import UPLOAD_FOLDER

            file = request.files['profile_image']
            filename = secure_filename(file.filename)
            file_path = os.path.join(UPLOAD_FOLDER, 'profile_images', filename)

            # Ensure directory exists
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            file.save(file_path)
            current_user.profile_image = f'profile_images/{filename}'

        db.session.commit()
        flash('Profile updated successfully', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/profile.html', title='My Profile')

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if not current_user.check_password(current_password):
            flash('Current password is incorrect', 'danger')
            return redirect(url_for('auth.change_password'))

        if new_password != confirm_password:
            flash('New passwords do not match', 'danger')
            return redirect(url_for('auth.change_password'))

        current_user.set_password(new_password)
        db.session.commit()

        # Log the password change activity
        log_activity(
            action='update',
            entity_type='password',
            entity_id=current_user.id,
            description=f'تم تغيير كلمة المرور للمستخدم: {current_user.username}'
        )

        flash('Password changed successfully', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', title='Change Password')


