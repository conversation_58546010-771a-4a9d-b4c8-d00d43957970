from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import datetime
import os
from werkzeug.utils import secure_filename

from app import db
from app.models.meeting import Meeting, MeetingAttachment, MeetingSummary
from app.models.user import User
from app.models.client import Client
from app.utils.notifications import send_notification, send_notification_to_multiple_users
from app.utils.activity_logger import log_activity

meeting_bp = Blueprint('meeting', __name__, url_prefix='/meetings')

@meeting_bp.route('/')
@login_required
def index():
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    location_filter = request.args.get('location', '')

    # Build the query
    query = Meeting.query

    # If user is not admin or manager, only show meetings they are invited to
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        query = query.join(Meeting.attendees).filter(User.id == current_user.id)

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Meeting.title.like(search_term)) |
            (Meeting.description.like(search_term)) |
            (Meeting.location.like(search_term))
        )

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Meeting.date >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Meeting.date <= to_date)
        except ValueError:
            pass

    # Apply location filter if provided
    if location_filter:
        query = query.filter(Meeting.location == location_filter)

    # Order by date and start_time (newest first)
    query = query.order_by(Meeting.date.desc(), Meeting.start_time.desc())

    # Paginate the results
    meetings = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get unique locations for filter
    locations = db.session.query(Meeting.location).distinct().all()
    locations = [loc[0] for loc in locations if loc[0]]

    return render_template('meeting/index.html',
                          title='الاجتماعات',
                          meetings=meetings,
                          locations=locations,
                          search_query=search_query,
                          date_from=date_from,
                          date_to=date_to,
                          location_filter=location_filter,
                          current_per_page=per_page)

@meeting_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Only admin and manager can create meetings
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لإنشاء اجتماعات', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get all users and clients for the form (filter inactive users for non-admin/manager)
    if current_user.has_role('admin') or current_user.has_role('manager'):
        users = User.query.all()
    else:
        users = User.query.filter(User.is_active == True).all()
    clients = Client.query.all()

    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        date = request.form.get('date')
        start_time = request.form.get('start_time')
        end_time = request.form.get('end_time')
        location = request.form.get('location')
        external_attendees = request.form.get('external_attendees')
        external_link = request.form.get('external_link')
        attachments_link = request.form.get('attachments_link')

        # Get selected users and clients
        user_ids = request.form.getlist('user_ids')
        client_ids = request.form.getlist('client_ids')

        # Validate date and time
        try:
            meeting_date = datetime.strptime(date, '%Y-%m-%d').date()
            meeting_start_time = datetime.strptime(start_time, '%H:%M').time()
            meeting_end_time = datetime.strptime(end_time, '%H:%M').time()

            if meeting_start_time > meeting_end_time:
                flash('وقت البدء يجب أن يكون قبل وقت الانتهاء', 'danger')
                return redirect(url_for('meeting.create'))
        except ValueError:
            flash('تنسيق التاريخ أو الوقت غير صحيح', 'danger')
            return redirect(url_for('meeting.create'))

        # Create meeting
        meeting = Meeting(
            title=title,
            description=description,
            date=meeting_date,
            start_time=meeting_start_time,
            end_time=meeting_end_time,
            location=location,
            external_attendees=external_attendees,
            external_link=external_link,
            attachments_link=attachments_link,
            created_by_id=current_user.id
        )

        # Add selected users
        for user_id in user_ids:
            user = User.query.get(user_id)
            if user:
                meeting.attendees.append(user)

        # Add selected clients
        for client_id in client_ids:
            client = Client.query.get(client_id)
            if client:
                meeting.clients.append(client)

        db.session.add(meeting)
        db.session.commit()

        # Log the activity
        log_activity(
            action='create',
            entity_type='meeting',
            entity_id=meeting.id,
            description=f'تم إنشاء اجتماع جديد: {meeting.title}'
        )

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'meetings', str(meeting.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = MeetingAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'meetings', str(meeting.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        meeting_id=meeting.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

            db.session.commit()

        # Send notifications to attendees
        attendee_ids = [user.id for user in meeting.attendees]
        if attendee_ids:
            send_notification_to_multiple_users(
                user_ids=attendee_ids,
                title='دعوة اجتماع جديدة',
                message=f'تمت دعوتك لحضور اجتماع "{meeting.title}" في {meeting.date} من {meeting.start_time} إلى {meeting.end_time}',
                notification_type='meeting'
            )

        flash('تم إنشاء الاجتماع بنجاح', 'success')
        return redirect(url_for('meeting.index'))

    return render_template('meeting/create.html', title='إنشاء اجتماع', users=users, clients=clients)

@meeting_bp.route('/view/<int:id>')
@login_required
def view(id):
    meeting = Meeting.query.get_or_404(id)

    # Check if user has permission to view this meeting
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in meeting.attendees):
        flash('ليس لديك صلاحية لعرض هذا الاجتماع', 'danger')
        return redirect(url_for('dashboard.index'))

    return render_template('meeting/view.html',
                          title=f'اجتماع: {meeting.title}',
                          meeting=meeting,
                          MeetingSummary=MeetingSummary)

@meeting_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    # Only admin and manager can edit meetings
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لتعديل الاجتماعات', 'danger')
        return redirect(url_for('dashboard.index'))

    meeting = Meeting.query.get_or_404(id)

    # Get all users and clients for the form (filter inactive users for non-admin/manager)
    if current_user.has_role('admin') or current_user.has_role('manager'):
        users = User.query.all()
    else:
        users = User.query.filter(User.is_active == True).all()
    clients = Client.query.all()

    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        date = request.form.get('date')
        start_time = request.form.get('start_time')
        end_time = request.form.get('end_time')
        location = request.form.get('location')
        external_attendees = request.form.get('external_attendees')
        external_link = request.form.get('external_link')
        attachments_link = request.form.get('attachments_link')

        # Get selected users and clients
        user_ids = request.form.getlist('user_ids')
        client_ids = request.form.getlist('client_ids')

        # Validate date and time
        try:
            meeting_date = datetime.strptime(date, '%Y-%m-%d').date()
            meeting_start_time = datetime.strptime(start_time, '%H:%M').time()
            meeting_end_time = datetime.strptime(end_time, '%H:%M').time()

            if meeting_start_time > meeting_end_time:
                flash('وقت البدء يجب أن يكون قبل وقت الانتهاء', 'danger')
                return redirect(url_for('meeting.edit', id=id))
        except ValueError:
            flash('تنسيق التاريخ أو الوقت غير صحيح', 'danger')
            return redirect(url_for('meeting.edit', id=id))

        # Update meeting
        meeting.title = title
        meeting.description = description
        meeting.date = meeting_date
        meeting.start_time = meeting_start_time
        meeting.end_time = meeting_end_time
        meeting.location = location
        meeting.external_attendees = external_attendees
        meeting.external_link = external_link
        meeting.attachments_link = attachments_link
        meeting.updated_at = datetime.utcnow()

        # Update attendees
        # First, get the current attendees for notification purposes
        old_attendees = set(meeting.attendees)

        # Clear current attendees and clients
        meeting.attendees = []
        meeting.clients = []

        # Add selected users
        for user_id in user_ids:
            user = User.query.get(user_id)
            if user:
                meeting.attendees.append(user)

        # Add selected clients
        for client_id in client_ids:
            client = Client.query.get(client_id)
            if client:
                meeting.clients.append(client)

        # Process attachments
        if 'attachments' in request.files:
            attachments = request.files.getlist('attachments')

            # Create directory for files if it doesn't exist
            upload_dir = os.path.join('app', 'static', 'uploads', 'meetings', str(meeting.id))
            os.makedirs(upload_dir, exist_ok=True)

            for attachment in attachments:
                if attachment and attachment.filename:
                    filename = secure_filename(attachment.filename)
                    file_path = os.path.join(upload_dir, filename)
                    attachment.save(file_path)

                    # Save file information to database
                    file_attachment = MeetingAttachment(
                        filename=filename,
                        file_path=os.path.join('uploads', 'meetings', str(meeting.id), filename),
                        file_type=attachment.content_type if hasattr(attachment, 'content_type') else None,
                        file_size=len(attachment.read()) if hasattr(attachment, 'read') else 0,
                        meeting_id=meeting.id,
                        uploaded_by_id=current_user.id
                    )
                    db.session.add(file_attachment)

        db.session.commit()

        # Log the activity
        log_activity(
            action='update',
            entity_type='meeting',
            entity_id=meeting.id,
            description=f'تم تحديث الاجتماع: {meeting.title}'
        )

        # Send notifications to new attendees
        new_attendees = set(meeting.attendees) - old_attendees
        removed_attendees = old_attendees - set(meeting.attendees)

        # Notify new attendees
        for attendee in new_attendees:
            send_notification(
                user_id=attendee.id,
                title='دعوة اجتماع جديدة',
                message=f'تمت دعوتك لحضور اجتماع "{meeting.title}" في {meeting.date} من {meeting.start_time} إلى {meeting.end_time}',
                notification_type='meeting'
            )

        # Notify removed attendees
        for attendee in removed_attendees:
            send_notification(
                user_id=attendee.id,
                title='تم إلغاء دعوة الاجتماع',
                message=f'تم إلغاء دعوتك لحضور اجتماع "{meeting.title}" في {meeting.date}',
                notification_type='meeting'
            )

        # Notify existing attendees about changes
        existing_attendees = set(meeting.attendees) & old_attendees
        for attendee in existing_attendees:
            send_notification(
                user_id=attendee.id,
                title='تم تحديث تفاصيل الاجتماع',
                message=f'تم تحديث تفاصيل اجتماع "{meeting.title}" في {meeting.date} من {meeting.start_time} إلى {meeting.end_time}',
                notification_type='meeting'
            )

        flash('تم تحديث الاجتماع بنجاح', 'success')
        return redirect(url_for('meeting.view', id=id))

    return render_template('meeting/edit.html', title=f'تعديل اجتماع: {meeting.title}',
                          meeting=meeting, users=users, clients=clients)

@meeting_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    # Only admin and manager can delete meetings
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لحذف الاجتماعات', 'danger')
        return redirect(url_for('dashboard.index'))

    meeting = Meeting.query.get_or_404(id)

    # Notify attendees about meeting cancellation
    for attendee in meeting.attendees:
        send_notification(
            user_id=attendee.id,
            title='تم إلغاء الاجتماع',
            message=f'تم إلغاء اجتماع "{meeting.title}" الذي كان مقرراً في {meeting.date}',
            notification_type='meeting'
        )

    # Delete attachments from filesystem
    for attachment in meeting.attachments:
        file_path = os.path.join('app', 'static', attachment.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)

    # Store meeting info before deletion for logging
    meeting_title = meeting.title
    meeting_id = meeting.id

    # Delete meeting
    db.session.delete(meeting)
    db.session.commit()

    # Log the activity
    log_activity(
        action='delete',
        entity_type='meeting',
        entity_id=meeting_id,
        description=f'تم حذف الاجتماع: {meeting_title}'
    )

    flash('تم حذف الاجتماع بنجاح', 'success')
    return redirect(url_for('meeting.index'))

@meeting_bp.route('/delete_attachment/<int:attachment_id>', methods=['POST'])
@login_required
def delete_attachment(attachment_id):
    # Only admin and manager can delete attachments
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لحذف المرفقات', 'danger')
        return redirect(url_for('dashboard.index'))

    attachment = MeetingAttachment.query.get_or_404(attachment_id)
    meeting_id = attachment.meeting_id

    # Delete file from filesystem
    file_path = os.path.join('app', 'static', attachment.file_path)
    if os.path.exists(file_path):
        os.remove(file_path)

    # Delete from database
    db.session.delete(attachment)
    db.session.commit()

    flash('تم حذف المرفق بنجاح', 'success')
    return redirect(url_for('meeting.edit', id=meeting_id))


@meeting_bp.route('/add_summary/<int:meeting_id>', methods=['POST'])
@login_required
def add_summary(meeting_id):
    meeting = Meeting.query.get_or_404(meeting_id)

    # Check if user has permission to add summary (must be an attendee, admin, or manager)
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in meeting.attendees):
        flash('ليس لديك صلاحية لإضافة ملخص لهذا الاجتماع', 'danger')
        return redirect(url_for('dashboard.index'))

    content = request.form.get('summary_content')
    if not content:
        flash('محتوى الملخص مطلوب', 'danger')
        return redirect(url_for('meeting.view', id=meeting_id))

    # Create new summary
    summary = MeetingSummary(
        content=content,
        meeting_id=meeting_id,
        created_by_id=current_user.id
    )

    db.session.add(summary)
    db.session.commit()

    # Log the activity
    log_activity(
        action='create',
        entity_type='meeting_summary',
        entity_id=summary.id,
        description=f'تم إضافة ملخص جديد للاجتماع: {meeting.title}'
    )

    flash('تم إضافة الملخص بنجاح', 'success')
    return redirect(url_for('meeting.view', id=meeting_id))


@meeting_bp.route('/edit_summary/<int:summary_id>', methods=['GET', 'POST'])
@login_required
def edit_summary(summary_id):
    summary = MeetingSummary.query.get_or_404(summary_id)
    meeting_id = summary.meeting_id

    # Check if user has permission to edit summary (must be the creator, admin, or manager)
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            summary.created_by_id == current_user.id):
        flash('ليس لديك صلاحية لتعديل هذا الملخص', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        content = request.form.get('summary_content')
        if not content:
            flash('محتوى الملخص مطلوب', 'danger')
            return redirect(url_for('meeting.edit_summary', summary_id=summary_id))

        # Update summary
        summary.content = content
        summary.updated_at = datetime.utcnow()

        db.session.commit()

        # Log the activity
        log_activity(
            action='update',
            entity_type='meeting_summary',
            entity_id=summary.id,
            description=f'تم تحديث ملخص الاجتماع: {summary.meeting.title}'
        )

        flash('تم تحديث الملخص بنجاح', 'success')
        return redirect(url_for('meeting.view', id=meeting_id))

    return render_template('meeting/edit_summary.html',
                          title='تعديل ملخص الاجتماع',
                          summary=summary)


@meeting_bp.route('/delete_summary/<int:summary_id>', methods=['GET', 'POST'])
@login_required
def delete_summary(summary_id):
    summary = MeetingSummary.query.get_or_404(summary_id)
    meeting_id = summary.meeting_id

    # Check if user has permission to delete summary (must be the creator, admin, or manager)
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            summary.created_by_id == current_user.id):
        flash('ليس لديك صلاحية لحذف هذا الملخص', 'danger')
        return redirect(url_for('dashboard.index'))

    if request.method == 'POST':
        # Store summary info before deletion for logging
        summary_id_for_log = summary.id
        meeting_title = summary.meeting.title

        # Delete summary
        db.session.delete(summary)
        db.session.commit()

        # Log the activity
        log_activity(
            action='delete',
            entity_type='meeting_summary',
            entity_id=summary_id_for_log,
            description=f'تم حذف ملخص الاجتماع: {meeting_title}'
        )

        flash('تم حذف الملخص بنجاح', 'success')
        return redirect(url_for('meeting.view', id=meeting_id))

    return render_template('meeting/delete_summary.html',
                          title='حذف ملخص الاجتماع',
                          summary=summary)
