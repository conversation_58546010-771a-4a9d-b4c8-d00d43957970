"""
مسارات النظام المحاسبي الجديد
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.accounting import Account, AccountType, AccountingSettings
from app.models.finance import PayrollEntry, Transaction, TransactionItem
from app.models.project import Project
from app.models.task import Task
from app.models.user import User
from datetime import datetime
import json

accounting_bp = Blueprint('accounting', __name__, url_prefix='/accounting')

# ==================== شجرة الحسابات ====================

@accounting_bp.route('/accounts')
@login_required
def accounts():
    """صفحة شجرة الحسابات"""
    # الحصول على الحسابات الرئيسية (بدون أب)
    root_accounts = Account.query.filter_by(parent_id=None, is_active=True).order_by(Account.code).all()
    
    return render_template('accounting/accounts.html',
                          title='شجرة الحسابات',
                          accounts=root_accounts,
                          account_types=AccountType.get_choices())

@accounting_bp.route('/accounts/create', methods=['GET', 'POST'])
@login_required
def create_account():
    """إنشاء حساب جديد"""
    if request.method == 'POST':
        code = request.form.get('code', '').strip()
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        account_type = request.form.get('account_type')
        parent_id = request.form.get('parent_id')
        
        # التحقق من البيانات
        if not code or not name or not account_type:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
            return redirect(url_for('accounting.create_account'))
        
        # التحقق من عدم تكرار الرمز
        if Account.query.filter_by(code=code).first():
            flash('رمز الحساب موجود مسبقاً', 'danger')
            return redirect(url_for('accounting.create_account'))
        
        try:
            # إنشاء الحساب
            account = Account(
                code=code,
                name=name,
                description=description,
                account_type=account_type,
                parent_id=int(parent_id) if parent_id else None,
                created_by_id=current_user.id
            )
            
            db.session.add(account)
            db.session.commit()
            
            flash('تم إنشاء الحساب بنجاح', 'success')
            return redirect(url_for('accounting.accounts'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء الحساب: {str(e)}', 'danger')
    
    # الحصول على الحسابات للاختيار كحساب أب
    parent_accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    return render_template('accounting/create_account.html',
                          title='إنشاء حساب جديد',
                          account_types=AccountType.get_choices(),
                          parent_accounts=parent_accounts)

@accounting_bp.route('/accounts/<int:account_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_account(account_id):
    """تعديل حساب"""
    account = Account.query.get_or_404(account_id)
    
    if request.method == 'POST':
        code = request.form.get('code', '').strip()
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        account_type = request.form.get('account_type')
        parent_id = request.form.get('parent_id')
        
        # التحقق من البيانات
        if not code or not name or not account_type:
            flash('جميع الحقول المطلوبة يجب ملؤها', 'danger')
            return redirect(url_for('accounting.edit_account', account_id=account_id))
        
        # التحقق من عدم تكرار الرمز (باستثناء الحساب الحالي)
        existing_account = Account.query.filter_by(code=code).first()
        if existing_account and existing_account.id != account.id:
            flash('رمز الحساب موجود مسبقاً', 'danger')
            return redirect(url_for('accounting.edit_account', account_id=account_id))
        
        try:
            # تحديث الحساب
            account.code = code
            account.name = name
            account.description = description
            account.account_type = account_type
            account.parent_id = int(parent_id) if parent_id else None
            account.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            flash('تم تحديث الحساب بنجاح', 'success')
            return redirect(url_for('accounting.accounts'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الحساب: {str(e)}', 'danger')
    
    # الحصول على الحسابات للاختيار كحساب أب (باستثناء الحساب نفسه وأطفاله)
    excluded_ids = [account.id] + [child.id for child in account.get_all_children()]
    parent_accounts = Account.query.filter(
        Account.is_active == True,
        ~Account.id.in_(excluded_ids)
    ).order_by(Account.code).all()
    
    return render_template('accounting/edit_account.html',
                          title=f'تعديل الحساب - {account.name}',
                          account=account,
                          account_types=AccountType.get_choices(),
                          parent_accounts=parent_accounts)

@accounting_bp.route('/accounts/<int:account_id>/delete', methods=['POST'])
@login_required
def delete_account(account_id):
    """حذف حساب"""
    account = Account.query.get_or_404(account_id)
    
    # التحقق من إمكانية الحذف
    can_delete, message = account.can_delete()
    
    if not can_delete:
        flash(message, 'danger')
        return redirect(url_for('accounting.accounts'))
    
    try:
        account.is_active = False  # حذف منطقي
        account.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('تم حذف الحساب بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الحساب: {str(e)}', 'danger')
    
    return redirect(url_for('accounting.accounts'))

@accounting_bp.route('/accounts/api/tree')
@login_required
def accounts_tree_api():
    """API للحصول على شجرة الحسابات"""
    def build_tree(parent_id=None):
        accounts = Account.query.filter_by(parent_id=parent_id, is_active=True).order_by(Account.code).all()
        tree = []
        
        for account in accounts:
            node = {
                'id': account.id,
                'code': account.code,
                'name': account.name,
                'full_name': account.full_name,
                'balance': float(account.balance),
                'account_type': account.account_type,
                'account_type_display': AccountType.get_display_name(account.account_type),
                'level': account.level,
                'has_children': account.has_children,
                'children': build_tree(account.id)
            }
            tree.append(node)
        
        return tree
    
    return jsonify(build_tree())

# ==================== الإعدادات ====================

@accounting_bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """إعدادات النظام المحاسبي"""
    settings = AccountingSettings.get_settings()
    
    if request.method == 'POST':
        try:
            # إعدادات الرواتب
            settings.payroll_debit_account_id = request.form.get('payroll_debit_account_id') or None
            settings.payroll_pending_account_id = request.form.get('payroll_pending_account_id') or None
            settings.payroll_paid_account_id = request.form.get('payroll_paid_account_id') or None
            
            # إعدادات الفواتير
            settings.invoice_pending_account_id = request.form.get('invoice_pending_account_id') or None
            settings.invoice_paid_account_id = request.form.get('invoice_paid_account_id') or None
            settings.invoice_overdue_account_id = request.form.get('invoice_overdue_account_id') or None
            
            settings.updated_at = datetime.utcnow()
            db.session.commit()
            
            flash('تم حفظ الإعدادات بنجاح', 'success')
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'danger')
    
    # الحصول على جميع الحسابات
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    return render_template('accounting/settings.html',
                          title='إعدادات النظام المحاسبي',
                          settings=settings,
                          accounts=accounts)

# ==================== لوحة التحكم ====================

@accounting_bp.route('/')
@accounting_bp.route('/dashboard')
@login_required
def dashboard():
    """لوحة تحكم النظام المحاسبي"""
    # إحصائيات سريعة
    total_accounts = Account.query.filter_by(is_active=True).count()
    total_payrolls = PayrollEntry.query.count()
    total_transactions = Transaction.query.count()
    
    # الحسابات الرئيسية
    main_accounts = Account.query.filter_by(parent_id=None, is_active=True).order_by(Account.code).all()
    
    # آخر المعاملات
    recent_transactions = Transaction.query.order_by(Transaction.created_at.desc()).limit(5).all()
    
    # آخر الرواتب
    recent_payrolls = PayrollEntry.query.order_by(PayrollEntry.created_at.desc()).limit(5).all()
    
    return render_template('accounting/dashboard.html',
                          title='لوحة تحكم النظام المحاسبي',
                          total_accounts=total_accounts,
                          total_payrolls=total_payrolls,
                          total_transactions=total_transactions,
                          main_accounts=main_accounts,
                          recent_transactions=recent_transactions,
                          recent_payrolls=recent_payrolls)
