{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء تايم لاين جديد - {{ project.name }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم التايم لاين <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required
                                   placeholder="أدخل اسم التايم لاين...">
                            <div class="form-text">اختر اسماً وصفياً للتايم لاين يوضح الغرض منه</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">وصف التايم لاين</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="أدخل وصفاً للتايم لاين (اختياري)..."></textarea>
                            <div class="form-text">وصف مختصر لما يغطيه هذا التايم لاين</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>إنشاء التايم لاين
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        ما هو التايم لاين؟
                    </h5>
                </div>
                <div class="card-body">
                    <p>التايم لاين هو أداة لتنظيم وتتبع خطوات المشروع بطريقة مرئية وسهلة الفهم. يمكنك:</p>
                    <ul>
                        <li><strong>إنشاء خطوات يدوية:</strong> خطوات مخصصة مع روابط ووصف</li>
                        <li><strong>ربط المهام الموجودة:</strong> ربط خطوات التايم لاين بالمهام الفعلية في المشروع</li>
                        <li><strong>تتبع التقدم:</strong> مراقبة نسبة الإنجاز وحالة كل خطوة</li>
                        <li><strong>التعاون:</strong> السماح لأعضاء الفريق بتحديث حالة الخطوات</li>
                    </ul>
                    <div class="alert alert-warning">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>نصيحة:</strong> يمكنك إنشاء عدة تايم لاين للمشروع الواحد لتنظيم مراحل مختلفة من العمل.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
