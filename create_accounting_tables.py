#!/usr/bin/env python3
"""
سكريبت لإنشاء جداول النظام المحاسبي الجديد
"""

from app import create_app, db
from app.models.accounting import Account, AccountType, AccountingSettings
from app.models.finance import PayrollEntry, Transaction, TransactionItem
from app.models.user import User
from datetime import datetime

def create_accounting_tables():
    """إنشاء جداول النظام المحاسبي"""
    try:
        print("🔧 إنشاء جداول النظام المحاسبي...")
        
        # إنشاء الجداول
        db.create_all()
        
        print("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
        
        # إنشاء حسابات افتراضية
        create_default_accounts()
        
        # إنشاء إعدادات افتراضية
        create_default_settings()
        
        print("🎉 تم إعداد النظام المحاسبي بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جداول النظام المحاسبي: {e}")
        db.session.rollback()
        return False

def create_default_accounts():
    """إنشاء حسابات افتراضية"""
    try:
        print("📊 إنشاء الحسابات الافتراضية...")
        
        # الحصول على أول مستخدم كمنشئ
        user = User.query.first()
        if not user:
            print("⚠️ لا يوجد مستخدمين في النظام")
            return
        
        # الحسابات الرئيسية
        main_accounts = [
            {
                'code': '1000',
                'name': 'الأصول',
                'account_type': AccountType.ASSETS,
                'description': 'الأصول الرئيسية للشركة'
            },
            {
                'code': '1100',
                'name': 'الأصول المتداولة',
                'account_type': AccountType.ASSETS,
                'description': 'الأصول قصيرة المدى',
                'parent_code': '1000'
            },
            {
                'code': '1110',
                'name': 'النقدية',
                'account_type': AccountType.CASH,
                'description': 'النقدية في الصندوق',
                'parent_code': '1100'
            },
            {
                'code': '1120',
                'name': 'البنوك',
                'account_type': AccountType.BANK,
                'description': 'الحسابات البنكية',
                'parent_code': '1100'
            },
            {
                'code': '1130',
                'name': 'المدينون',
                'account_type': AccountType.RECEIVABLES,
                'description': 'المبالغ المستحقة من العملاء',
                'parent_code': '1100'
            },
            {
                'code': '2000',
                'name': 'الخصوم',
                'account_type': AccountType.LIABILITIES,
                'description': 'التزامات الشركة'
            },
            {
                'code': '2100',
                'name': 'الخصوم المتداولة',
                'account_type': AccountType.LIABILITIES,
                'description': 'الالتزامات قصيرة المدى',
                'parent_code': '2000'
            },
            {
                'code': '2110',
                'name': 'الدائنون',
                'account_type': AccountType.PAYABLES,
                'description': 'المبالغ المستحقة للموردين',
                'parent_code': '2100'
            },
            {
                'code': '3000',
                'name': 'حقوق الملكية',
                'account_type': AccountType.EQUITY,
                'description': 'حقوق أصحاب الشركة'
            },
            {
                'code': '4000',
                'name': 'الإيرادات',
                'account_type': AccountType.REVENUE,
                'description': 'إيرادات الشركة'
            },
            {
                'code': '5000',
                'name': 'المصروفات',
                'account_type': AccountType.EXPENSES,
                'description': 'مصروفات الشركة'
            },
            {
                'code': '5100',
                'name': 'مصروفات الرواتب',
                'account_type': AccountType.EXPENSES,
                'description': 'رواتب الموظفين',
                'parent_code': '5000'
            },
            {
                'code': '5110',
                'name': 'الرواتب المعلقة',
                'account_type': AccountType.EXPENSES,
                'description': 'الرواتب المعلقة في انتظار الدفع',
                'parent_code': '5100'
            },
            {
                'code': '5120',
                'name': 'الرواتب المدفوعة',
                'account_type': AccountType.EXPENSES,
                'description': 'الرواتب المدفوعة فعلياً',
                'parent_code': '5100'
            },
            {
                'code': '4100',
                'name': 'إيرادات الفواتير',
                'account_type': AccountType.REVENUE,
                'description': 'إيرادات من الفواتير',
                'parent_code': '4000'
            },
            {
                'code': '4110',
                'name': 'الفواتير المعلقة',
                'account_type': AccountType.REVENUE,
                'description': 'الفواتير في انتظار الدفع',
                'parent_code': '4100'
            },
            {
                'code': '4120',
                'name': 'الفواتير المدفوعة',
                'account_type': AccountType.REVENUE,
                'description': 'الفواتير المدفوعة',
                'parent_code': '4100'
            },
            {
                'code': '4130',
                'name': 'الفواتير المتأخرة',
                'account_type': AccountType.REVENUE,
                'description': 'الفواتير المتأخرة في الدفع',
                'parent_code': '4100'
            }
        ]
        
        # إنشاء الحسابات
        created_accounts = {}
        
        for account_data in main_accounts:
            # البحث عن الحساب الأب إذا كان موجوداً
            parent_id = None
            if 'parent_code' in account_data:
                parent_code = account_data['parent_code']
                if parent_code in created_accounts:
                    parent_id = created_accounts[parent_code].id
            
            # التحقق من عدم وجود الحساب مسبقاً
            existing_account = Account.query.filter_by(code=account_data['code']).first()
            if existing_account:
                created_accounts[account_data['code']] = existing_account
                continue
            
            # إنشاء الحساب
            account = Account(
                code=account_data['code'],
                name=account_data['name'],
                description=account_data['description'],
                account_type=account_data['account_type'],
                parent_id=parent_id,
                created_by_id=user.id
            )
            
            db.session.add(account)
            db.session.flush()  # للحصول على ID
            
            created_accounts[account_data['code']] = account
            print(f"  ✅ تم إنشاء الحساب: {account.code} - {account.name}")
        
        db.session.commit()
        print(f"✅ تم إنشاء {len(created_accounts)} حساب افتراضي")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات الافتراضية: {e}")
        db.session.rollback()

def create_default_settings():
    """إنشاء إعدادات افتراضية"""
    try:
        print("⚙️ إنشاء الإعدادات الافتراضية...")
        
        # التحقق من وجود إعدادات مسبقاً
        existing_settings = AccountingSettings.query.first()
        if existing_settings:
            print("⚠️ الإعدادات موجودة مسبقاً")
            return
        
        # الحصول على أول مستخدم
        user = User.query.first()
        if not user:
            print("⚠️ لا يوجد مستخدمين في النظام")
            return
        
        # الحصول على الحسابات الافتراضية
        cash_account = Account.query.filter_by(code='1110').first()  # النقدية
        payroll_pending = Account.query.filter_by(code='5110').first()  # الرواتب المعلقة
        payroll_paid = Account.query.filter_by(code='5120').first()  # الرواتب المدفوعة
        invoice_pending = Account.query.filter_by(code='4110').first()  # الفواتير المعلقة
        invoice_paid = Account.query.filter_by(code='4120').first()  # الفواتير المدفوعة
        invoice_overdue = Account.query.filter_by(code='4130').first()  # الفواتير المتأخرة
        
        # إنشاء الإعدادات
        settings = AccountingSettings(
            payroll_debit_account_id=cash_account.id if cash_account else None,
            payroll_pending_account_id=payroll_pending.id if payroll_pending else None,
            payroll_paid_account_id=payroll_paid.id if payroll_paid else None,
            invoice_pending_account_id=invoice_pending.id if invoice_pending else None,
            invoice_paid_account_id=invoice_paid.id if invoice_paid else None,
            invoice_overdue_account_id=invoice_overdue.id if invoice_overdue else None,
            created_by_id=user.id
        )
        
        db.session.add(settings)
        db.session.commit()
        
        print("✅ تم إنشاء الإعدادات الافتراضية")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات الافتراضية: {e}")
        db.session.rollback()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إعداد النظام المحاسبي الجديد...\n")
    
    # إنشاء تطبيق Flask
    app = create_app()
    
    with app.app_context():
        success = create_accounting_tables()
        
        if success:
            print("\n🎉 تم إعداد النظام المحاسبي بنجاح!")
            print("\n📋 الحسابات المتاحة:")
            print("   - شجرة الحسابات: /accounting/accounts")
            print("   - إدارة الرواتب: /payroll")
            print("   - لوحة التحكم: /accounting")
            print("   - الإعدادات: /accounting/settings")
            return True
        else:
            print("\n💥 فشل في إعداد النظام المحاسبي!")
            return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
