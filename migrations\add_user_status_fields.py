"""
Migration to add status fields to users table
"""

from app import db
from sqlalchemy import text

def run_migration():
    """Add status fields to users table"""
    try:
        # Check if status column exists
        result = db.session.execute(text("PRAGMA table_info(users)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'status' not in columns:
            # Add status column
            db.session.execute(text("ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'offline'"))
            print("Added status column to users table")
        
        if 'custom_status' not in columns:
            # Add custom_status column
            db.session.execute(text("ALTER TABLE users ADD COLUMN custom_status VARCHAR(255)"))
            print("Added custom_status column to users table")
        
        if 'last_activity' not in columns:
            # Add last_activity column
            db.session.execute(text("ALTER TABLE users ADD COLUMN last_activity DATETIME DEFAULT CURRENT_TIMESTAMP"))
            print("Added last_activity column to users table")
        
        # Update existing users to have default status
        db.session.execute(text("UPDATE users SET status = 'offline' WHERE status IS NULL"))
        db.session.execute(text("UPDATE users SET last_activity = CURRENT_TIMESTAMP WHERE last_activity IS NULL"))
        
        db.session.commit()
        print("User status fields migration completed successfully")
        
    except Exception as e:
        print(f"Error in user status fields migration: {e}")
        db.session.rollback()

if __name__ == '__main__':
    run_migration()
