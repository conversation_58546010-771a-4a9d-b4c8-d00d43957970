from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db, login_manager

# Association table for user roles
UserRole = db.Table('user_roles',
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>('users.id'), primary_key=True),
    db.Column('role_id', db.Integer, db.<PERSON>ey('roles.id'), primary_key=True)
)

class Role(db.Model):
    __tablename__ = 'roles'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(255))

    def __repr__(self):
        return f'<Role {self.name}>'

class User(db.Model, UserMixin):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    # Nuevos campos para nombre en inglés
    first_name_en = db.Column(db.String(64))
    last_name_en = db.Column(db.String(64))
    # Full name fields (4-part name)
    full_name_ar = db.Column(db.String(255))  # Full 4-part name in Arabic
    full_name_en = db.Column(db.String(255))  # Full 4-part name in English
    phone = db.Column(db.String(20))
    profile_image = db.Column(db.String(255), default='default.jpg')  # Path to image file (legacy support)
    profile_image_data = db.Column(db.LargeBinary)  # Binary image data
    profile_image_mime = db.Column(db.String(64))  # MIME type of the image
    bio = db.Column(db.Text)
    # Nuevos campos adicionales
    birth_date = db.Column(db.Date)
    nationality = db.Column(db.String(64))
    bank_account = db.Column(db.String(255))
    date_joined = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    is_active = db.Column(db.Boolean, default=True)

    # Status/Connection fields
    status = db.Column(db.String(20), default='offline')  # online, busy, away, offline
    custom_status = db.Column(db.String(255))  # Custom status message
    last_activity = db.Column(db.DateTime, default=datetime.utcnow)

    # CV/Resume field - single text field for simplicity
    cv = db.Column(db.Text)

    # Relationships
    roles = db.relationship('Role', secondary=UserRole, lazy='subquery',
                           backref=db.backref('users', lazy=True))
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    department = db.relationship('Department', foreign_keys=[department_id], backref='members')

    # Projects assigned to this user
    projects = db.relationship('Project', secondary='project_members',
                              backref=db.backref('members', lazy='dynamic'))

    # Tasks assigned to this user
    tasks = db.relationship('Task', backref='assignee', lazy='dynamic')

    # Notifications for this user
    notifications = db.relationship('Notification', backref='user', lazy='dynamic')

    # ID Documents for this user
    id_documents = db.relationship('IdDocument', backref='user', lazy='dynamic', cascade='all, delete-orphan')

    # Honorary ranks for this user
    honorary_ranks = db.relationship(
        'HonoraryRank',
        secondary='user_honorary_ranks',
        primaryjoin="User.id == user_honorary_ranks.c.user_id",
        secondaryjoin="HonoraryRank.id == user_honorary_ranks.c.honorary_rank_id",
        lazy='subquery',
        backref=db.backref('users', lazy=True)
    )

    def __repr__(self):
        return f'<User {self.username}>'

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def has_role(self, role_name):
        return any(role.name == role_name for role in self.roles)

    def add_role(self, role):
        if not self.has_role(role.name):
            self.roles.append(role)

    def remove_role(self, role):
        # Protect GolDeN user from losing admin role
        if self.username == 'GolDeN' and role.name == 'admin':
            return False

        if self.has_role(role.name):
            self.roles.remove(role)
            return True
        return False

    def get_full_name(self, current_user=None):
        """
        الحصول على الاسم الكامل للمستخدم مع مراعاة صلاحيات المستخدم الحالي

        Args:
            current_user: المستخدم الحالي الذي يطلب الاسم (إذا كان None، سيتم استخدام current_user من flask_login)
        """
        from flask_login import current_user as flask_current_user

        # استخدم المستخدم المقدم أو المستخدم الحالي من flask_login
        viewer = current_user if current_user is not None else flask_current_user

        # إذا كان المستخدم غير مسجل دخول، أعد الاسم الأول والأخير فقط
        if not hasattr(viewer, 'has_role'):
            if self.first_name and self.last_name:
                return f"{self.first_name} {self.last_name}"
            return self.username

        # إذا كان المستخدم هو نفسه أو لديه صلاحية admin أو manager، أعد الاسم الكامل
        if viewer.id == self.id or viewer.has_role('admin') or viewer.has_role('manager'):
            # First priority: use full Arabic name if available
            if self.full_name_ar:
                return self.full_name_ar

        # لجميع المستخدمين الآخرين (department_head وما تحتها)، أعد الاسم الأول والأخير فقط
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"

        # Fallback to username
        return self.username

    def get_full_name_en(self, current_user=None):
        """
        الحصول على الاسم الكامل للمستخدم باللغة الإنجليزية مع مراعاة صلاحيات المستخدم الحالي

        Args:
            current_user: المستخدم الحالي الذي يطلب الاسم (إذا كان None، سيتم استخدام current_user من flask_login)
        """
        from flask_login import current_user as flask_current_user

        # استخدم المستخدم المقدم أو المستخدم الحالي من flask_login
        viewer = current_user if current_user is not None else flask_current_user

        # إذا كان المستخدم غير مسجل دخول، أعد الاسم الأول والأخير فقط
        if not hasattr(viewer, 'has_role'):
            if self.first_name_en and self.last_name_en:
                return f"{self.first_name_en} {self.last_name_en}"
            elif self.first_name and self.last_name:
                return f"{self.first_name} {self.last_name}"
            return self.username

        # إذا كان المستخدم هو نفسه أو لديه صلاحية admin أو manager، أعد الاسم الكامل
        if viewer.id == self.id or viewer.has_role('admin') or viewer.has_role('manager'):
            # First priority: use full English name if available
            if self.full_name_en:
                return self.full_name_en

        # لجميع المستخدمين الآخرين (department_head وما تحتها)، أعد الاسم الأول والأخير فقط
        if self.first_name_en and self.last_name_en:
            return f"{self.first_name_en} {self.last_name_en}"
        elif self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"

        # Fallback to username
        return self.username

    def set_profile_image(self, image_data, mime_type):
        """
        تخزين بيانات الصورة في قاعدة البيانات

        Args:
            image_data: بيانات الصورة الثنائية
            mime_type: نوع الملف (مثل image/jpeg)
        """
        self.profile_image_data = image_data
        self.profile_image_mime = mime_type

    def get_profile_image_data(self):
        """
        استرجاع بيانات الصورة من قاعدة البيانات

        Returns:
            tuple: (بيانات الصورة الثنائية, نوع الملف) أو None إذا لم تكن هناك صورة
        """
        if self.profile_image_data and self.profile_image_mime:
            return (self.profile_image_data, self.profile_image_mime)
        return None

    def has_profile_image_in_db(self):
        """
        التحقق مما إذا كان المستخدم لديه صورة مخزنة في قاعدة البيانات

        Returns:
            bool: True إذا كان المستخدم لديه صورة مخزنة في قاعدة البيانات
        """
        return self.profile_image_data is not None and len(self.profile_image_data) > 0

    def set_status(self, status, custom_message=None):
        """
        تحديث حالة الاتصال للمستخدم

        Args:
            status: حالة الاتصال (online, busy, away, offline)
            custom_message: رسالة مخصصة (اختيارية)
        """
        self.status = status
        self.custom_status = custom_message
        self.last_activity = datetime.utcnow()

    def get_status_display(self):
        """
        الحصول على نص حالة الاتصال للعرض

        Returns:
            str: نص حالة الاتصال
        """
        status_map = {
            'online': 'متصل',
            'busy': 'مشغول',
            'away': 'ساكن',
            'offline': 'غير متصل'
        }

        base_status = status_map.get(self.status, 'غير متصل')

        if self.custom_status and self.status != 'offline':
            return f"{base_status} - {self.custom_status}"

        return base_status

    def get_status_color(self):
        """
        الحصول على لون حالة الاتصال

        Returns:
            str: لون CSS للحالة
        """
        color_map = {
            'online': 'success',
            'busy': 'danger',
            'away': 'warning',
            'offline': 'secondary'
        }
        return color_map.get(self.status, 'secondary')

    def is_online(self):
        """
        التحقق مما إذا كان المستخدم متصل

        Returns:
            bool: True إذا كان المستخدم متصل
        """
        return self.status == 'online'

    def update_last_activity(self):
        """
        تحديث وقت آخر نشاط
        """
        self.last_activity = datetime.utcnow()

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))
