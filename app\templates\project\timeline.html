{% extends 'base.html' %}

{% block styles %}
<style>
.timeline-container {
    position: relative;
    padding: 20px 0;
}

.timeline-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
}

.timeline-item {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.timeline-title {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 15px 20px;
    margin: 0;
    font-weight: bold;
}

.timeline-steps {
    padding: 20px;
}

.step-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 10px;
    background: #f8f9fa;
    border-left: 4px solid #dee2e6;
    transition: all 0.3s ease;
}

.step-item.pending {
    border-left-color: #6c757d;
    background: #f8f9fa;
}

.step-item.in_progress {
    border-left-color: #007bff;
    background: #e3f2fd;
}

.step-item.completed {
    border-left-color: #28a745;
    background: #e8f5e8;
}

.step-item.cancelled {
    border-left-color: #dc3545;
    background: #ffeaea;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 16px;
    color: white;
}

.step-icon.pending {
    background: #6c757d;
}

.step-icon.in_progress {
    background: #007bff;
}

.step-icon.completed {
    background: #28a745;
}

.step-icon.cancelled {
    background: #dc3545;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.step-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.step-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.step-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.progress-bar-container {
    background: #e9ecef;
    border-radius: 10px;
    height: 8px;
    margin: 15px 0;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.empty-timeline {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-timeline i {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.btn-floating {
    position: fixed;
    bottom: 30px;
    left: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 1000;
}

/* Dropdown improvements */
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
    padding: 10px 0;
    min-width: 200px;
}

.dropdown-item {
    padding: 8px 20px;
    border-radius: 5px;
    margin: 2px 10px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    transform: translateX(-3px);
}

.dropdown-item.active {
    background: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

.dropdown-header {
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 8px 20px 5px;
}

@media (max-width: 768px) {
    .timeline-steps {
        padding: 15px;
    }

    .step-item {
        flex-direction: column;
        text-align: center;
    }

    .step-icon {
        margin: 0 0 15px 0;
    }

    .step-actions {
        margin-top: 15px;
        justify-content: center;
    }

    .dropdown-menu {
        min-width: 180px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="timeline-container">
    <!-- Header -->
    <div class="timeline-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">
                    <i class="fas fa-project-diagram me-2"></i>
                    التايم لاين - {{ project.name }}
                </h1>
                <p class="mb-0 opacity-75">تتبع تقدم المشروع خطوة بخطوة</p>
            </div>
            <div>
                <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-right me-1"></i>العودة للمشروع
                </a>
                {% if current_user.has_role('admin') or current_user.has_role('manager') or
                      current_user in project.managers or current_user.id == project.manager_id %}
                <a href="{{ url_for('project.create_timeline', id=project.id) }}" class="btn btn-warning">
                    <i class="fas fa-plus me-1"></i>إنشاء تايم لاين جديد
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    {% if timelines %}
        {% for timeline in timelines %}
        <div class="timeline-item">
            <h3 class="timeline-title">
                <i class="fas fa-timeline me-2"></i>
                {{ timeline.name }}
                <span class="float-end">
                    <small>{{ timeline.get_completion_percentage() }}% مكتمل</small>
                </span>
            </h3>
            
            {% if timeline.description %}
            <div class="px-3 pt-3">
                <p class="text-muted">{{ timeline.description }}</p>
            </div>
            {% endif %}
            
            <!-- Progress Bar -->
            <div class="px-3">
                <div class="progress-bar-container">
                    <div class="progress-bar" style="width: {{ timeline.get_completion_percentage() }}%"></div>
                </div>
            </div>
            
            <div class="timeline-steps">
                {% set steps = timeline.get_steps_ordered() %}
                {% if steps %}
                    {% for step in steps %}
                    <div class="step-item {{ step.status }}" id="step-{{ step.id }}">
                        <div class="step-icon {{ step.status }}">
                            <i class="{{ step.get_status_icon() }}"></i>
                        </div>
                        
                        <div class="step-content">
                            <div class="step-title">{{ step.title }}</div>
                            {% if step.description %}
                            <div class="step-description">{{ step.description }}</div>
                            {% endif %}
                            
                            <div class="step-meta">
                                <span>
                                    <i class="fas fa-tag me-1"></i>
                                    {{ step.get_status_display() }}
                                </span>
                                <span>
                                    <i class="fas fa-user me-1"></i>
                                    {% if step.source_type == 'task' and step.task %}
                                        صاحب المهمة: {{ step.task.assigned_to.get_full_name() if step.task.assigned_to else 'غير محدد' }}
                                    {% else %}
                                        منشئ الخطوة: {{ step.created_by.get_full_name() }}
                                    {% endif %}
                                </span>
                                {% if step.source_type == 'task' and step.task %}
                                <span>
                                    <i class="fas fa-tasks me-1"></i>
                                    <a href="{{ url_for('project.view_task', task_id=step.task.id) }}" target="_blank" class="text-decoration-none">
                                        مرتبط بمهمة
                                    </a>
                                </span>
                                {% endif %}
                            </div>
                            
                            {% if step.get_links_list() %}
                            <div class="mt-2">
                                {% for link in step.get_links_list() %}
                                <a href="{{ link }}" target="_blank" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-external-link-alt me-1"></i>رابط {{ loop.index }}
                                </a>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="step-actions">
                            {% if current_user.has_role('admin') or current_user.has_role('manager') or
                                  current_user in project.managers or current_user.id == project.manager_id or
                                  current_user in project.members %}
                            <a href="{{ url_for('project.manage_timeline_step', step_id=step.id) }}"
                               class="btn btn-sm btn-outline-primary shadow-sm" title="إدارة الخطوة">
                                <i class="fas fa-cog me-1"></i>إدارة
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-tasks text-muted" style="font-size: 48px;"></i>
                    <p class="text-muted mt-3">لا توجد خطوات في هذا التايم لاين بعد</p>
                    {% if current_user.has_role('admin') or current_user.has_role('manager') or
                          current_user in project.managers or current_user.id == project.manager_id %}
                    <a href="{{ url_for('project.add_timeline_step', timeline_id=timeline.id) }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>إضافة خطوة
                    </a>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if steps and (current_user.has_role('admin') or current_user.has_role('manager') or
                      current_user in project.managers or current_user.id == project.manager_id) %}
                <div class="text-center mt-3">
                    <a href="{{ url_for('project.add_timeline_step', timeline_id=timeline.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>إضافة خطوة جديدة
                    </a>
                    <button class="btn btn-outline-info" onclick="syncTimeline({{ timeline.id }})">
                        <i class="fas fa-sync me-1"></i>مزامنة مع المهام
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    {% else %}
    <div class="empty-timeline">
        <i class="fas fa-project-diagram"></i>
        <h3>لا توجد تايم لاين لهذا المشروع بعد</h3>
        <p>ابدأ بإنشاء تايم لاين لتتبع تقدم المشروع</p>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or
              current_user in project.managers or current_user.id == project.manager_id %}
        <a href="{{ url_for('project.create_timeline', id=project.id) }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>إنشاء تايم لاين جديد
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function syncTimeline(timelineId) {
    fetch(`/project/timeline/${timelineId}/sync`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Refresh page to show updated data
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء المزامنة');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    const container = document.querySelector('.timeline-container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function syncTimeline(timelineId) {
    fetch(`/project/timeline/${timelineId}/sync`, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            // Refresh page to show updated data
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'حدث خطأ أثناء المزامنة');
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    const container = document.querySelector('.timeline-container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Add smooth scrolling for better UX
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to timeline items
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 200);
    });
});
</script>
{% endblock %}
