{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus me-2"></i>
                        إضافة خطوة جديدة - {{ timeline.name }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" id="stepForm">
                        <!-- Source Type Selection -->
                        <div class="mb-4">
                            <label class="form-label">نوع الخطوة <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-primary h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="source_task" 
                                                   name="source_type" value="task" required>
                                            <label for="source_task" class="form-check-label d-block mt-2">
                                                <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                                                <h5>ربط بمهمة موجودة</h5>
                                                <p class="text-muted">ربط الخطوة بمهمة من مهام المشروع مع مزامنة تلقائية للحالة</p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-warning h-100">
                                        <div class="card-body text-center">
                                            <input type="radio" class="form-check-input" id="source_manual" 
                                                   name="source_type" value="manual" required>
                                            <label for="source_manual" class="form-check-label d-block mt-2">
                                                <i class="fas fa-edit fa-2x text-warning mb-2"></i>
                                                <h5>خطوة يدوية</h5>
                                                <p class="text-muted">إنشاء خطوة مخصصة مع إمكانية إضافة روابط ووصف</p>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Task Selection (for task source type) -->
                        <div id="taskSection" class="mb-3" style="display: none;">
                            <label for="task_id" class="form-label">اختر المهمة <span class="text-danger">*</span></label>
                            <select class="form-select" id="task_id" name="task_id">
                                <option value="">-- اختر مهمة --</option>
                                {% for task in available_tasks %}
                                <option value="{{ task.id }}">
                                    {{ task.title }} 
                                    <span class="text-muted">({{ task.status }})</span>
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">سيتم جلب عنوان ووصف وحالة المهمة تلقائياً</div>
                        </div>

                        <!-- Manual Step Fields (for manual source type) -->
                        <div id="manualSection" style="display: none;">
                            <div class="mb-3">
                                <label for="title" class="form-label">عنوان الخطوة <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" name="title"
                                       placeholder="أدخل عنوان الخطوة...">
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">وصف الخطوة</label>
                                <textarea class="form-control" id="description" name="description" rows="4"
                                          placeholder="أدخل وصف الخطوة (اختياري)..."></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="links" class="form-label">الروابط (اختياري)</label>
                                <textarea class="form-control" id="links" name="links" rows="3"
                                          placeholder="أدخل الروابط مفصولة بفواصل...&#10;مثال:&#10;https://example.com,&#10;https://docs.google.com/document/123"></textarea>
                                <div class="form-text">يمكنك إضافة عدة روابط مفصولة بفواصل</div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-1"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-1"></i>إضافة الخطوة
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="card mt-4" id="previewCard" style="display: none;">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-eye me-2"></i>
                        معاينة الخطوة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="step-preview" id="stepPreview">
                        <!-- Preview content will be inserted here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const sourceTypeRadios = document.querySelectorAll('input[name="source_type"]');
    const taskSection = document.getElementById('taskSection');
    const manualSection = document.getElementById('manualSection');
    const taskSelect = document.getElementById('task_id');
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');
    const previewCard = document.getElementById('previewCard');
    const stepPreview = document.getElementById('stepPreview');

    // Handle source type change
    sourceTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'task') {
                taskSection.style.display = 'block';
                manualSection.style.display = 'none';
                // Make task selection required
                taskSelect.required = true;
                titleInput.required = false;
            } else if (this.value === 'manual') {
                taskSection.style.display = 'none';
                manualSection.style.display = 'block';
                // Make title required for manual steps
                taskSelect.required = false;
                titleInput.required = true;
            }
            updatePreview();
        });
    });

    // Handle task selection change
    taskSelect.addEventListener('change', updatePreview);
    
    // Handle manual input changes
    titleInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);

    function updatePreview() {
        const selectedSourceType = document.querySelector('input[name="source_type"]:checked');
        
        if (!selectedSourceType) {
            previewCard.style.display = 'none';
            return;
        }

        let previewContent = '';
        
        if (selectedSourceType.value === 'task') {
            const selectedTask = taskSelect.options[taskSelect.selectedIndex];
            if (taskSelect.value) {
                previewContent = `
                    <div class="step-item in_progress">
                        <div class="step-icon in_progress">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">${selectedTask.text.split(' (')[0]}</div>
                            <div class="step-description">مرتبط بمهمة من المشروع</div>
                            <div class="step-meta">
                                <span><i class="fas fa-tag me-1"></i>سيتم مزامنة الحالة تلقائياً</span>
                                <span><i class="fas fa-tasks me-1"></i>مرتبط بمهمة</span>
                            </div>
                        </div>
                    </div>
                `;
            }
        } else if (selectedSourceType.value === 'manual') {
            const title = titleInput.value || 'عنوان الخطوة';
            const description = descriptionInput.value || 'وصف الخطوة';
            
            previewContent = `
                <div class="step-item pending">
                    <div class="step-icon pending">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="step-content">
                        <div class="step-title">${title}</div>
                        <div class="step-description">${description}</div>
                        <div class="step-meta">
                            <span><i class="fas fa-tag me-1"></i>معلق</span>
                            <span><i class="fas fa-edit me-1"></i>خطوة يدوية</span>
                        </div>
                    </div>
                </div>
            `;
        }

        if (previewContent) {
            stepPreview.innerHTML = previewContent;
            previewCard.style.display = 'block';
        } else {
            previewCard.style.display = 'none';
        }
    }

    // Form validation
    document.getElementById('stepForm').addEventListener('submit', function(e) {
        const selectedSourceType = document.querySelector('input[name="source_type"]:checked');
        
        if (!selectedSourceType) {
            e.preventDefault();
            alert('يرجى اختيار نوع الخطوة');
            return;
        }

        if (selectedSourceType.value === 'task' && !taskSelect.value) {
            e.preventDefault();
            alert('يرجى اختيار مهمة');
            return;
        }

        if (selectedSourceType.value === 'manual' && !titleInput.value.trim()) {
            e.preventDefault();
            alert('يرجى إدخال عنوان الخطوة');
            return;
        }
    });
});
</script>

<style>
.step-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background: #f8f9fa;
    border-left: 4px solid #dee2e6;
}

.step-item.pending {
    border-left-color: #6c757d;
    background: #f8f9fa;
}

.step-item.in_progress {
    border-left-color: #007bff;
    background: #e3f2fd;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 15px;
    font-size: 16px;
    color: white;
}

.step-icon.pending {
    background: #6c757d;
}

.step-icon.in_progress {
    background: #007bff;
}

.step-content {
    flex: 1;
}

.step-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.step-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.step-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #888;
}

.card.border-primary:hover,
.card.border-warning:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}
</style>
{% endblock %}
