from flask import Blueprint, render_template, redirect, url_for, flash, request, send_file
from flask_login import login_required, current_user
from datetime import datetime, timezone
import os
from werkzeug.utils import secure_filename
from sqlalchemy import case

from app import db
from app.utils.activity_logger import log_activity
from app.models.project import Project, ProjectFile, ProjectUpdate
from app.models.task import Task
from app.models.user import User
from app.models.department import Department
from app.models.client import Client
from app.config import UPLOAD_FOLDER

project_bp = Blueprint('project', __name__, url_prefix='/projects')

@project_bp.route('/')
@login_required
def index():
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    status_filter = request.args.get('status', '')

    # Start building the query
    if current_user.has_role('admin') or current_user.has_role('manager'):
        # Admins and managers see all projects
        query = Project.query
    elif current_user.has_role('department_head'):
        # Department heads see their department's projects
        if current_user.managed_department:
            # If they are managing a department, show all projects in that department
            # but exclude cancelled projects unless specifically filtered
            if status_filter == 'cancelled':
                query = Project.query.filter_by(department_id=current_user.managed_department.id)
            else:
                query = Project.query.filter_by(department_id=current_user.managed_department.id).filter(Project.status != 'cancelled')
        else:
            # Otherwise, show their assigned projects
            # but exclude cancelled projects unless specifically filtered
            if status_filter == 'cancelled':
                query = Project.query.filter(Project.members.contains(current_user))
            else:
                query = Project.query.filter(Project.members.contains(current_user)).filter(Project.status != 'cancelled')
    else:
        # Regular employees see only their assigned projects
        # but exclude cancelled projects unless specifically filtered
        if status_filter == 'cancelled':
            query = Project.query.filter(Project.members.contains(current_user))
        else:
            query = Project.query.filter(Project.members.contains(current_user)).filter(Project.status != 'cancelled')

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Project.name.like(search_term)) |
            (Project.description.like(search_term))
        )

    # Apply status filter if provided
    if status_filter and status_filter != 'all':
        query = query.filter(Project.status == status_filter)

    # Order by start date (newest first)
    query = query.order_by(Project.start_date.desc())

    # Paginate the results
    projects = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('project/index.html',
                          title='Projects',
                          projects=projects,
                          search_query=search_query,
                          status_filter=status_filter,
                          current_per_page=per_page)

@project_bp.route('/view/<int:id>')
@login_required
def view(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to view this project
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects):
        flash('ليس لديك صلاحية لعرض هذا المشروع', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرضه', 'warning')
        return redirect(url_for('project.index'))

    # Get clients for invoice creation
    clients = Client.query.all()

    # Get today's date for default issue date
    today = datetime.now().strftime('%Y-%m-%d')

    # Import ProjectUpdate for template
    from app.models.project import ProjectUpdate

    # Get query parameters for tasks pagination and filtering
    tasks_page = request.args.get('tasks_page', 1, type=int)
    tasks_per_page = request.args.get('tasks_per_page', 5, type=int)
    tasks_status_filter = request.args.get('tasks_status', '')
    tasks_priority_filter = request.args.get('tasks_priority', '')
    tasks_assignee_filter = request.args.get('tasks_assignee', '')

    # Build the tasks query
    tasks_query = Task.query.filter_by(project_id=id)

    # Apply status filter if provided
    if tasks_status_filter and tasks_status_filter != 'all':
        tasks_query = tasks_query.filter(Task.status == tasks_status_filter)

    # Apply priority filter if provided
    if tasks_priority_filter and tasks_priority_filter != 'all':
        tasks_query = tasks_query.filter(Task.priority == tasks_priority_filter)

    # Apply assignee filter if provided
    if tasks_assignee_filter and tasks_assignee_filter != 'all':
        tasks_query = tasks_query.filter(Task.assignee_id == int(tasks_assignee_filter))

    # Order by due date (closest first) and then by priority (high to low)
    tasks_query = tasks_query.order_by(
        case(
            (Task.due_date == None, 1),
            else_=0
        ),
        Task.due_date,
        case(
            (Task.priority == 'high', 0),
            (Task.priority == 'medium', 1),
            (Task.priority == 'low', 2),
            else_=3
        )
    )

    # Paginate the tasks
    tasks = tasks_query.paginate(page=tasks_page, per_page=tasks_per_page, error_out=False)

    # Get all project members for assignee filter
    project_members = project.members.all()

    return render_template('project/view.html', title=f'Project: {project.name}',
                          project=project, clients=clients, today=today, ProjectUpdate=ProjectUpdate,
                          tasks=tasks, tasks_status_filter=tasks_status_filter,
                          tasks_priority_filter=tasks_priority_filter, tasks_assignee_filter=tasks_assignee_filter,
                          project_members=project_members, tasks_per_page=tasks_per_page)

@project_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    # Check if user has permission to create projects
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('لا تملك صلاحية إنشاء مشاريع جديدة', 'danger')
        return redirect(url_for('project.index'))

    departments = Department.query.all()
    clients = Client.query.all()

    # Get all users for project members (filter inactive users for non-admin/manager)
    if current_user.has_role('admin') or current_user.has_role('manager'):
        all_users = User.query.all()
        potential_managers = User.query.all()
    else:
        all_users = User.query.filter(User.is_active == True).all()
        potential_managers = User.query.filter(User.is_active == True).all()

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        invoice_approval_date_str = request.form.get('invoice_approval_date')
        status = request.form.get('status')
        priority = request.form.get('priority')
        department_id = request.form.get('department_id')  # Legacy single department
        client_id = request.form.get('client_id')
        manager_id = request.form.get('manager_id')  # Legacy single manager

        # Get multiple managers and departments
        manager_ids = request.form.getlist('manager_ids')
        department_ids = request.form.getlist('department_ids')

        # Get selected members
        member_ids = request.form.getlist('member_ids')

        # Parse dates
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else datetime.now(datetime.timezone.utc)
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else None
        invoice_approval_date = datetime.strptime(invoice_approval_date_str, '%Y-%m-%d') if invoice_approval_date_str else None

        # Create new project
        project = Project(
            name=name,
            description=description,
            start_date=start_date,
            end_date=end_date,
            invoice_approval_date=invoice_approval_date,
            status=status,
            priority=priority
        )

        # Set legacy department if provided
        if department_id:
            project.department_id = department_id

        # Set multiple departments if provided
        for dept_id in department_ids:
            department = Department.query.get(dept_id)
            if department and department not in project.departments:
                project.departments.append(department)

        # Set client if provided
        if client_id:
            project.client_id = client_id

        # Set legacy manager if provided
        if manager_id:
            project.manager_id = manager_id

        # Set multiple managers if provided
        for mgr_id in manager_ids:
            manager = User.query.get(mgr_id)
            if manager and manager not in project.managers:
                project.managers.append(manager)

        # Add current user as a project member if not already a manager
        if current_user not in project.managers and str(current_user.id) not in manager_ids:
            project.members.append(current_user)

        # Add selected members to project
        for member_id in member_ids:
            member = User.query.get(member_id)
            if member and member not in project.members:
                project.members.append(member)

        db.session.add(project)
        db.session.commit()

        flash('Project created successfully', 'success')
        return redirect(url_for('project.view', id=project.id))

    return render_template('project/create.html', title='Create Project',
                          departments=departments, clients=clients, potential_managers=potential_managers,
                          all_users=all_users, now=datetime.now(timezone.utc))

@project_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to edit this project
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user.has_role('department_head')):
        flash('You do not have permission to edit this project', 'danger')
        return redirect(url_for('project.view', id=id))

    departments = Department.query.all()
    clients = Client.query.all()

    # Get all users for project members (filter inactive users for non-admin/manager)
    if current_user.has_role('admin') or current_user.has_role('manager'):
        all_users = User.query.all()
        potential_managers = User.query.all()
    else:
        all_users = User.query.filter(User.is_active == True).all()
        potential_managers = User.query.filter(User.is_active == True).all()

    if request.method == 'POST':
        project.name = request.form.get('name')
        project.description = request.form.get('description')

        # Parse dates
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        invoice_approval_date_str = request.form.get('invoice_approval_date')
        project.start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else project.start_date
        project.end_date = datetime.strptime(end_date_str, '%Y-%m-%d') if end_date_str else project.end_date
        project.invoice_approval_date = datetime.strptime(invoice_approval_date_str, '%Y-%m-%d') if invoice_approval_date_str else project.invoice_approval_date

        project.status = request.form.get('status')
        project.priority = request.form.get('priority')

        # Update legacy department if provided
        department_id = request.form.get('department_id')
        if department_id:
            project.department_id = department_id
        else:
            project.department_id = None

        # Update multiple departments
        department_ids = request.form.getlist('department_ids')
        # Clear current departments
        project.departments = []
        # Add selected departments
        for dept_id in department_ids:
            department = Department.query.get(dept_id)
            if department:
                project.departments.append(department)

        # Update client if provided
        client_id = request.form.get('client_id')
        if client_id:
            project.client_id = client_id
        else:
            project.client_id = None

        # Update legacy manager if provided (admin only)
        if current_user.has_role('admin'):
            manager_id = request.form.get('manager_id')
            if manager_id:
                project.manager_id = manager_id
            else:
                project.manager_id = None  # Allow removing manager

        # Update multiple managers (admin only)
        if current_user.has_role('admin'):
            manager_ids = request.form.getlist('manager_ids')
            # Clear current managers
            project.managers = []
            # Add selected managers
            for mgr_id in manager_ids:
                manager = User.query.get(mgr_id)
                if manager:
                    project.managers.append(manager)

        # Update project members
        member_ids = request.form.getlist('member_ids')

        # Clear current members
        project.members = []

        # Add selected members
        for member_id in member_ids:
            member = User.query.get(member_id)
            if member:
                project.members.append(member)

        db.session.commit()

        flash('Project updated successfully', 'success')
        return redirect(url_for('project.view', id=id))

    return render_template('project/edit.html', title=f'Edit Project: {project.name}',
                          project=project, departments=departments, clients=clients,
                          potential_managers=potential_managers, all_users=all_users,
                          now=datetime.now(timezone.utc))

@project_bp.route('/delete/<int:id>', methods=['GET', 'POST'])
@login_required
def delete(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to delete this project
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('ليس لديك صلاحية لحذف هذا المشروع', 'danger')
        return redirect(url_for('project.view', id=id))

    if request.method == 'POST':
        # Get project info before deletion for logging
        project_name = project.name
        project_id = project.id

        db.session.delete(project)
        db.session.commit()

        # Log the activity
        log_activity(
            action='delete',
            entity_type='project',
            entity_id=project_id,
            description=f'تم حذف المشروع: {project_name}'
        )

        flash('تم حذف المشروع بنجاح', 'success')
        return redirect(url_for('project.index'))

    # Count related items
    tasks_count = Task.query.filter_by(project_id=id).count()
    files_count = ProjectFile.query.filter_by(project_id=id).count()
    updates_count = ProjectUpdate.query.filter_by(project_id=id).count()

    return render_template('project/delete.html',
                          title='حذف المشروع',
                          project=project,
                          tasks_count=tasks_count,
                          files_count=files_count,
                          updates_count=updates_count)

@project_bp.route('/<int:id>/members')
@login_required
def members(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to view project members
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرض أعضائه', 'warning')
        return redirect(url_for('project.index'))

    return render_template('project/members.html', title=f'{project.name} Members', project=project)

@project_bp.route('/<int:id>/add_member', methods=['GET', 'POST'])
@login_required
def add_member(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to add project members
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user.has_role('department_head')):
        flash('You do not have permission to add project members', 'danger')
        return redirect(url_for('project.members', id=id))

    # Get all users who are not already in this project (filter inactive users for non-admin/manager)
    current_member_ids = [member.id for member in project.members]
    available_users_query = User.query.filter(~User.id.in_(current_member_ids))

    # Filter inactive users for non-admin/manager
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        available_users_query = available_users_query.filter(User.is_active == True)

    available_users = available_users_query.all()

    if request.method == 'POST':
        user_ids = request.form.getlist('user_ids')

        for user_id in user_ids:
            user = User.query.get(user_id)
            if user:
                project.members.append(user)

        db.session.commit()

        flash('Members added successfully', 'success')
        return redirect(url_for('project.members', id=id))

    return render_template('project/add_member.html', title=f'Add Members to {project.name}',
                          project=project, available_users=available_users)

@project_bp.route('/<int:project_id>/remove_member/<int:user_id>', methods=['GET', 'POST'])
@login_required
def remove_member(project_id, user_id):
    project = Project.query.get_or_404(project_id)

    # Check if user has permission to remove project members
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user.has_role('department_head')):
        flash('ليس لديك صلاحية لإزالة أعضاء المشروع', 'danger')
        return redirect(url_for('project.members', id=project_id))

    user = User.query.get_or_404(user_id)

    # Cannot remove project manager
    if user.id == project.manager_id:
        flash('لا يمكن إزالة مدير المشروع من المشروع', 'danger')
        return redirect(url_for('project.members', id=project_id))

    if request.method == 'POST':
        project.members.remove(user)
        db.session.commit()

        # Log the activity
        log_activity(
            action='remove_member',
            entity_type='project',
            entity_id=project_id,
            description=f'تمت إزالة {user.get_full_name()} من المشروع: {project.name}'
        )

        flash('تمت إزالة العضو بنجاح', 'success')
        return redirect(url_for('project.members', id=project_id))

    return render_template('project/remove_member.html',
                          title='إزالة عضو من المشروع',
                          project=project,
                          user=user)

@project_bp.route('/<int:id>/tasks')
@login_required
def tasks(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to view project tasks
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرض مهامه', 'warning')
        return redirect(url_for('project.index'))

    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    priority_filter = request.args.get('priority', '')
    assignee_filter = request.args.get('assignee', '')

    # Build the query
    query = Task.query.filter_by(project_id=id)

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            (Task.title.like(search_term)) |
            (Task.description.like(search_term))
        )

    # Apply status filter if provided
    if status_filter and status_filter != 'all':
        query = query.filter(Task.status == status_filter)

    # Apply priority filter if provided
    if priority_filter and priority_filter != 'all':
        query = query.filter(Task.priority == priority_filter)

    # Apply assignee filter if provided
    if assignee_filter and assignee_filter != 'all':
        query = query.filter(Task.assignee_id == assignee_filter)

    # Order by priority (high first) and then by due date (closest first)
    query = query.order_by(
        case(
            (Task.priority == 'high', 1),
            (Task.priority == 'medium', 2),
            (Task.priority == 'low', 3),
            else_=4
        ),
        Task.due_date.asc().nullslast()
    )

    # Paginate the results
    tasks = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get all project members for assignee filter
    project_members = project.members.all()

    return render_template('project/tasks.html',
                          title=f'{project.name} Tasks',
                          project=project,
                          tasks=tasks,
                          search_query=search_query,
                          status_filter=status_filter,
                          priority_filter=priority_filter,
                          assignee_filter=assignee_filter,
                          project_members=project_members,
                          current_per_page=per_page)

@project_bp.route('/<int:id>/create_task', methods=['GET', 'POST'])
@login_required
def create_task(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to create project tasks
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user.has_role('department_head')):
        flash('You do not have permission to create tasks', 'danger')
        return redirect(url_for('project.tasks', id=id))

    if request.method == 'POST':
        title = request.form.get('title')
        description = request.form.get('description')
        start_date_str = request.form.get('start_date')
        due_date_str = request.form.get('due_date')
        status = request.form.get('status')
        priority = request.form.get('priority')
        assignee_id = request.form.get('assignee_id')

        # Parse dates
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else datetime.now(timezone.utc)
        due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

        # Create new task
        task = Task(
            title=title,
            description=description,
            start_date=start_date,
            due_date=due_date,
            status=status,
            priority=priority,
            project_id=id
        )

        # Set assignee if provided
        if assignee_id:
            task.assignee_id = assignee_id

        db.session.add(task)
        db.session.commit()

        # Send notification to assignee if provided
        if assignee_id:
            from app.utils.notifications import send_notification

            # Get project name for the notification
            project_name = project.name

            # Format due date for notification
            due_date_formatted = due_date.strftime('%Y-%m-%d') if due_date else 'غير محدد'

            # Priority in Arabic
            priority_ar = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية'
            }.get(priority, 'متوسطة')

            # Send notification to assignee
            send_notification(
                user_id=assignee_id,
                title='تم تعيينك لمهمة جديدة',
                message=f'تم تعيينك لمهمة جديدة "{title}" في مشروع "{project_name}" بأولوية {priority_ar} وتاريخ استحقاق {due_date_formatted}',
                notification_type='task',
                related_task_id=task.id,
                related_project_id=id,
                link_url=url_for('project.tasks', id=id)
            )

        flash('Task created successfully', 'success')
        return redirect(url_for('project.tasks', id=id))

    return render_template('project/create_task.html', title=f'Create Task for {project.name}',
                          project=project, members=project.members, now=datetime.now(timezone.utc))

@project_bp.route('/<int:id>/files')
@login_required
def files(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to view project files
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرض ملفاته', 'warning')
        return redirect(url_for('project.index'))

    return render_template('project/files.html', title=f'{project.name} Files', project=project)

@project_bp.route('/<int:id>/upload_file', methods=['GET', 'POST'])
@login_required
def upload_file(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to upload project files
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or project in current_user.projects):
        flash('You do not have permission to upload files', 'danger')
        return redirect(url_for('project.files', id=id))

    if request.method == 'POST':
        # Get notes and links if provided
        notes = request.form.get('notes', '')
        links = request.form.get('links', '')

        # Check if file is provided
        file = None
        if 'file' in request.files:
            file = request.files['file']
            if file.filename == '':
                file = None

        # Check if we have at least one of: file, notes, or links
        if not file and not links and not notes:
            flash('يجب تحميل ملف أو إضافة ملاحظات أو روابط على الأقل', 'danger')
            return redirect(request.url)

        if file:
            filename = secure_filename(file.filename)

            # Create project files directory if it doesn't exist
            project_files_dir = os.path.join(UPLOAD_FOLDER, 'project_files', str(id))
            os.makedirs(project_files_dir, exist_ok=True)

            file_path = os.path.join(project_files_dir, filename)
            file.save(file_path)

            # Create file record in database
            project_file = ProjectFile(
                filename=filename,
                filepath=f'project_files/{id}/{filename}',
                file_type=file.content_type,
                file_size=os.path.getsize(file_path),
                notes=notes,
                links=links,
                project_id=id,
                uploaded_by_id=current_user.id
            )
        else:
            # Create a file record with just links
            project_file = ProjectFile(
                filename="روابط فقط",
                filepath="",
                file_type="links",
                file_size=0,
                notes=notes,
                links=links,
                project_id=id,
                uploaded_by_id=current_user.id
            )

        # Add the file record to the database and commit
        db.session.add(project_file)
        db.session.commit()

        if file:
            flash('تم رفع الملف بنجاح', 'success')
        else:
            flash('تم إضافة الملاحظات والروابط بنجاح', 'success')
        return redirect(url_for('project.files', id=id))

    return render_template('project/upload_file.html', title=f'Upload File for {project.name}', project=project)

@project_bp.route('/download_file/<int:file_id>')
@login_required
def download_file(file_id):
    """Descargar un archivo del proyecto"""
    project_file = ProjectFile.query.get_or_404(file_id)
    project_id = project_file.project_id
    project = Project.query.get_or_404(project_id)

    # Check if user has permission to view project files
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or project in current_user.projects):
        flash('ليس لديك صلاحية لعرض الملفات', 'danger')
        return redirect(url_for('project.files', id=project_id))

    # Check if file exists
    if not project_file.filepath:
        flash('الملف غير موجود', 'danger')
        return redirect(url_for('project.files', id=project_id))

    # Get the file path
    file_path = os.path.join(UPLOAD_FOLDER, project_file.filepath)

    # Check if file exists on disk
    if not os.path.exists(file_path):
        flash('الملف غير موجود على الخادم', 'danger')
        return redirect(url_for('project.files', id=project_id))

    # Return the file as an attachment with the correct filename and MIME type
    return send_file(file_path, as_attachment=True, download_name=project_file.filename, mimetype=project_file.file_type)

@project_bp.route('/delete_file/<int:file_id>', methods=['GET', 'POST'])
@login_required
def delete_file(file_id):
    project_file = ProjectFile.query.get_or_404(file_id)
    project_id = project_file.project_id

    # Get project information before potential detachment
    project = Project.query.get_or_404(project_id)
    project_name = project.name

    # Check if user has permission to delete project files
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or current_user.id == project_file.uploaded_by_id):
        flash('ليس لديك صلاحية لحذف الملفات', 'danger')
        return redirect(url_for('project.files', id=project_id))

    if request.method == 'POST':
        # Store file info before deletion for logging
        file_name = project_file.filename
        file_id_for_log = project_file.id

        # Check if this is a real file or just links
        if project_file.filepath and project_file.filepath.strip() and project_file.file_type != "links":
            # This is a real file, try to delete it from filesystem
            try:
                file_path = os.path.join(UPLOAD_FOLDER, project_file.filepath)
                if os.path.exists(file_path) and os.path.isfile(file_path):
                    os.remove(file_path)
            except (PermissionError, OSError) as e:
                # Log the error but continue with database deletion
                print(f"Error deleting file {file_path}: {str(e)}")
                # We don't want to show this technical error to the user
                # The file will still be deleted from the database

        # Delete from database
        db.session.delete(project_file)
        db.session.commit()

        # Log the activity
        log_activity(
            action='delete',
            entity_type='project_file',
            entity_id=file_id_for_log,
            description=f'تم حذف الملف: {file_name} من المشروع: {project_name}'
        )

        # Show appropriate message based on file type
        if project_file.file_type == "links":
            flash('تم حذف الروابط بنجاح', 'success')
        else:
            flash('تم حذف الملف بنجاح', 'success')

        return redirect(url_for('project.files', id=project_id))

    return render_template('project/delete_file.html',
                          title='حذف ملف',
                          project_file=project_file)

@project_bp.route('/view_task/<int:task_id>')
@login_required
def view_task(task_id):
    task = Task.query.get_or_404(task_id)
    project = task.project

    # Check if user has permission to view this task
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects or
            current_user.id == task.assignee_id):
        flash('ليس لديك صلاحية لعرض هذه المهمة', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرض مهامه', 'warning')
        return redirect(url_for('project.index'))

    return render_template('project/view_task.html',
                          title=f'مهمة: {task.title}',
                          task=task)


@project_bp.route('/update_task_status/<int:task_id>', methods=['POST'])
@login_required
def update_task_status(task_id):
    task = Task.query.get_or_404(task_id)
    project_id = task.project_id

    # Check if user has permission to update task status
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == task.project.manager_id or current_user.id == task.assignee_id):
        flash('ليس لديك صلاحية لتحديث حالة المهمة', 'danger')
        return redirect(url_for('project.tasks', id=project_id))

    status = request.form.get('status')
    if status in ['pending', 'in_progress', 'completed', 'cancelled']:
        task.status = status

        # If task is marked as completed, set completed_at timestamp
        if status == 'completed' and not task.completed_at:
            task.completed_at = datetime.now(timezone.utc)
        # If task is changed from completed to another status, clear completed_at
        elif status != 'completed' and task.completed_at:
            task.completed_at = None

        db.session.commit()

        # Send notification to project manager if task status is updated by assignee
        if current_user.id == task.assignee_id and task.project.manager_id:
            from app.utils.notifications import send_notification

            # Status in Arabic
            status_ar = {
                'pending': 'قيد الانتظار',
                'in_progress': 'قيد التنفيذ',
                'completed': 'مكتملة',
                'cancelled': 'ملغاة'
            }.get(status, status)

            # Send notification to project manager
            send_notification(
                user_id=task.project.manager_id,
                title='تم تحديث حالة المهمة',
                message=f'قام {current_user.get_full_name()} بتغيير حالة المهمة "{task.title}" إلى {status_ar}',
                notification_type='task',
                related_task_id=task.id,
                related_project_id=task.project_id,
                link_url=url_for('project.tasks', id=task.project_id)
            )

        flash('تم تحديث حالة المهمة بنجاح', 'success')
    else:
        flash('قيمة الحالة غير صالحة', 'danger')

    # Redirect back to the page the user came from
    referrer = request.referrer
    if referrer and 'view_task' in referrer:
        return redirect(url_for('project.view_task', task_id=task_id))
    else:
        return redirect(url_for('project.tasks', id=project_id))

@project_bp.route('/edit_task/<int:task_id>', methods=['GET', 'POST'])
@login_required
def edit_task(task_id):
    task = Task.query.get_or_404(task_id)
    project = task.project

    # Check if user has permission to edit task
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            current_user.has_role('department_head')):
        flash('You do not have permission to edit tasks', 'danger')
        return redirect(url_for('project.tasks', id=project.id))

    if request.method == 'POST':
        task.title = request.form.get('title')
        task.description = request.form.get('description')

        # Parse dates
        start_date_str = request.form.get('start_date')
        due_date_str = request.form.get('due_date')
        task.start_date = datetime.strptime(start_date_str, '%Y-%m-%d') if start_date_str else task.start_date
        task.due_date = datetime.strptime(due_date_str, '%Y-%m-%d') if due_date_str else None

        task.status = request.form.get('status')
        task.priority = request.form.get('priority')

        # Set assignee if provided
        assignee_id = request.form.get('assignee_id')
        if assignee_id:
            task.assignee_id = assignee_id
        else:
            task.assignee_id = None

        # If task is marked as completed, set completed_at timestamp
        if task.status == 'completed' and not task.completed_at:
            task.completed_at = datetime.now(timezone.utc)
        # If task is changed from completed to another status, clear completed_at
        elif task.status != 'completed' and task.completed_at:
            task.completed_at = None

        db.session.commit()

        # Send notification if assignee has changed
        if assignee_id and (not task.assignee_id or task.assignee_id != int(assignee_id)):
            from app.utils.notifications import send_notification

            # Get project name for the notification
            project_name = project.name

            # Format due date for notification
            due_date_formatted = task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد'

            # Priority in Arabic
            priority_ar = {
                'low': 'منخفضة',
                'medium': 'متوسطة',
                'high': 'عالية'
            }.get(task.priority, 'متوسطة')

            # Send notification to new assignee
            send_notification(
                user_id=assignee_id,
                title='تم تعيينك لمهمة',
                message=f'تم تعيينك لمهمة "{task.title}" في مشروع "{project_name}" بأولوية {priority_ar} وتاريخ استحقاق {due_date_formatted}',
                notification_type='task',
                related_task_id=task.id,
                related_project_id=project.id,
                link_url=url_for('project.tasks', id=project.id)
            )

        # Send notification to assignee if task details have changed but assignee is the same
        elif assignee_id and task.assignee_id == int(assignee_id):
            from app.utils.notifications import send_notification

            # Send notification about task update
            send_notification(
                user_id=assignee_id,
                title='تم تحديث تفاصيل المهمة',
                message=f'تم تحديث تفاصيل المهمة "{task.title}" في مشروع "{project.name}"',
                notification_type='task',
                related_task_id=task.id,
                related_project_id=project.id,
                link_url=url_for('project.tasks', id=project.id)
            )

        flash('Task updated successfully', 'success')
        return redirect(url_for('project.tasks', id=project.id))

    return render_template('project/edit_task.html', title=f'Edit Task: {task.title}',
                          task=task, project=project, members=project.members, now=datetime.now(timezone.utc))

@project_bp.route('/delete_task/<int:task_id>', methods=['GET', 'POST'])
@login_required
def delete_task(task_id):
    task = Task.query.get_or_404(task_id)
    project_id = task.project_id

    # Check if user has permission to delete task
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == task.project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department.id == task.project.department_id)):
        flash('ليس لديك صلاحية لحذف المهام', 'danger')
        return redirect(url_for('project.tasks', id=project_id))

    if request.method == 'POST':
        # Store task info before deletion for logging
        task_title = task.title
        task_id_for_log = task.id

        db.session.delete(task)
        db.session.commit()

        # Log the activity
        log_activity(
            action='delete',
            entity_type='task',
            entity_id=task_id_for_log,
            description=f'تم حذف المهمة: {task_title} من المشروع: {task.project.name}'
        )

        flash('تم حذف المهمة بنجاح', 'success')
        return redirect(url_for('project.tasks', id=project_id))

    return render_template('project/delete_task.html',
                          title='حذف المهمة',
                          task=task)


# Project Updates/Announcements Routes
@project_bp.route('/<int:id>/updates')
@login_required
def updates(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to view project updates
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or
            (current_user.has_role('department_head') and current_user.managed_department and
             project.department_id == current_user.managed_department.id) or
            project in current_user.projects):
        flash('ليس لديك صلاحية لعرض هذه الصفحة', 'danger')
        return redirect(url_for('project.index'))

    # Check if project is cancelled and user is not admin or manager
    if project.status == 'cancelled' and not (current_user.has_role('admin') or current_user.has_role('manager')):
        flash('هذا المشروع ملغي ولا يمكن عرض تحديثاته', 'warning')
        return redirect(url_for('project.index'))

    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)

    # Validate per_page to be one of the allowed values
    if per_page not in [25, 50, 100]:
        per_page = 25

    # Get pinned updates (these will always be shown at the top)
    pinned_updates = ProjectUpdate.query.filter_by(project_id=id, is_pinned=True).order_by(
        ProjectUpdate.created_at.desc()).all()

    # Get regular updates with pagination
    regular_updates_query = ProjectUpdate.query.filter_by(project_id=id, is_pinned=False).order_by(
        ProjectUpdate.created_at.desc())

    # Paginate the regular updates
    regular_updates_paginated = regular_updates_query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('project/updates.html',
                          title=f'تحديثات {project.name}',
                          project=project,
                          pinned_updates=pinned_updates,
                          regular_updates=regular_updates_paginated,
                          per_page=per_page)


@project_bp.route('/<int:id>/create_update', methods=['GET', 'POST'])
@login_required
def create_update(id):
    project = Project.query.get_or_404(id)

    # Check if user has permission to create project updates
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id):
        flash('ليس لديك صلاحية لإنشاء تحديثات للمشروع', 'danger')
        return redirect(url_for('project.updates', id=id))

    if request.method == 'POST':
        title = request.form.get('title')
        content = request.form.get('content')
        links = request.form.get('links')
        is_pinned = 'is_pinned' in request.form
        send_notification = 'send_notification' in request.form

        # Create new update
        update = ProjectUpdate(
            title=title,
            content=content,
            links=links,
            is_pinned=is_pinned,
            project_id=id,
            created_by_id=current_user.id
        )

        db.session.add(update)
        db.session.commit()

        # Send notifications to project members if requested
        if send_notification:
            from app.utils.notifications import send_notification_to_multiple_users

            # Get all project members
            member_ids = [member.id for member in project.members]

            # Add project managers if they're not already in members
            if project.manager_id and project.manager_id not in member_ids:
                member_ids.append(project.manager_id)

            for manager in project.managers:
                if manager.id not in member_ids:
                    member_ids.append(manager.id)

            # Don't send notification to the user who created the update
            if current_user.id in member_ids:
                member_ids.remove(current_user.id)

            if member_ids:
                send_notification_to_multiple_users(
                    user_ids=member_ids,
                    title=f'تحديث جديد في مشروع {project.name}',
                    message=f'{current_user.get_full_name()} قام بإضافة تحديث جديد: {title}',
                    notification_type='project_update',
                    related_project_id=project.id
                )

        flash('تم إنشاء التحديث بنجاح', 'success')
        return redirect(url_for('project.updates', id=id))

    return render_template('project/create_update.html', title=f'إنشاء تحديث لـ {project.name}', project=project)


@project_bp.route('/edit_update/<int:update_id>', methods=['GET', 'POST'])
@login_required
def edit_update(update_id):
    update = ProjectUpdate.query.get_or_404(update_id)
    project = update.project

    # Check if user has permission to edit project updates
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == project.manager_id or current_user.id == update.created_by_id):
        flash('ليس لديك صلاحية لتعديل هذا التحديث', 'danger')
        return redirect(url_for('project.updates', id=project.id))

    if request.method == 'POST':
        update.title = request.form.get('title')
        update.content = request.form.get('content')
        update.links = request.form.get('links')
        update.is_pinned = 'is_pinned' in request.form
        send_notification = 'send_notification' in request.form

        db.session.commit()

        # Send notifications to project members if requested
        if send_notification:
            from app.utils.notifications import send_notification_to_multiple_users

            # Get all project members
            member_ids = [member.id for member in project.members]

            # Add project managers if they're not already in members
            if project.manager_id and project.manager_id not in member_ids:
                member_ids.append(project.manager_id)

            for manager in project.managers:
                if manager.id not in member_ids:
                    member_ids.append(manager.id)

            # Don't send notification to the user who edited the update
            if current_user.id in member_ids:
                member_ids.remove(current_user.id)

            if member_ids:
                send_notification_to_multiple_users(
                    user_ids=member_ids,
                    title=f'تم تحديث منشور في مشروع {project.name}',
                    message=f'{current_user.get_full_name()} قام بتعديل منشور: {update.title}',
                    notification_type='project_update',
                    related_project_id=project.id
                )

        flash('تم تحديث المنشور بنجاح', 'success')
        return redirect(url_for('project.updates', id=project.id))

    return render_template('project/edit_update.html', title=f'تعديل التحديث: {update.title}', update=update, project=project)


@project_bp.route('/delete_update/<int:update_id>', methods=['GET', 'POST'])
@login_required
def delete_update(update_id):
    update = ProjectUpdate.query.get_or_404(update_id)
    project_id = update.project_id

    # Check if user has permission to delete project updates
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == update.project.manager_id or current_user.id == update.created_by_id):
        flash('ليس لديك صلاحية لحذف هذا التحديث', 'danger')
        return redirect(url_for('project.updates', id=project_id))

    if request.method == 'POST':
        db.session.delete(update)
        db.session.commit()

        flash('تم حذف التحديث بنجاح', 'success')
        return redirect(url_for('project.updates', id=project_id))

    return render_template('project/delete_update.html', title='حذف التحديث', update=update)


@project_bp.route('/toggle_pin_update/<int:update_id>', methods=['POST'])
@login_required
def toggle_pin_update(update_id):
    update = ProjectUpdate.query.get_or_404(update_id)
    project_id = update.project_id

    # Check if user has permission to pin/unpin project updates
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == update.project.manager_id):
        flash('ليس لديك صلاحية لتثبيت/إلغاء تثبيت هذا التحديث', 'danger')
        return redirect(url_for('project.updates', id=project_id))

    # Toggle pin status
    update.is_pinned = not update.is_pinned
    db.session.commit()

    if update.is_pinned:
        flash('تم تثبيت التحديث بنجاح', 'success')
    else:
        flash('تم إلغاء تثبيت التحديث بنجاح', 'success')

    return redirect(url_for('project.updates', id=project_id))

# Timeline Routes

@project_bp.route('/<int:id>/timeline')
@login_required
def timeline(id):
    """عرض التايم لاين للمشروع"""
    project = Project.query.get_or_404(id)

    # Check if user has permission to view project timeline
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.members or current_user in project.managers or
            current_user.id == project.manager_id):
        flash('ليس لديك صلاحية لعرض تايم لاين هذا المشروع', 'danger')
        return redirect(url_for('project.index'))

    # Get all timelines for this project
    from app.models.timeline import ProjectTimeline
    timelines = ProjectTimeline.query.filter_by(project_id=id, is_active=True).order_by(ProjectTimeline.order_index).all()

    return render_template('project/timeline.html',
                          title=f'التايم لاين - {project.name}',
                          project=project,
                          timelines=timelines)

@project_bp.route('/<int:id>/timeline/create', methods=['GET', 'POST'])
@login_required
def create_timeline(id):
    """إنشاء تايم لاين جديد"""
    project = Project.query.get_or_404(id)

    # Check if user has permission to create timeline
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id):
        flash('ليس لديك صلاحية لإنشاء تايم لاين لهذا المشروع', 'danger')
        return redirect(url_for('project.timeline', id=id))

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        if not name:
            flash('اسم التايم لاين مطلوب', 'danger')
            return redirect(url_for('project.create_timeline', id=id))

        # Get the next order index
        from app.models.timeline import ProjectTimeline
        max_order = db.session.query(db.func.max(ProjectTimeline.order_index)).filter_by(project_id=id).scalar() or 0

        timeline = ProjectTimeline(
            name=name,
            description=description,
            project_id=id,
            created_by_id=current_user.id,
            order_index=max_order + 1
        )

        db.session.add(timeline)
        db.session.commit()

        flash('تم إنشاء التايم لاين بنجاح', 'success')
        return redirect(url_for('project.timeline', id=id))

    return render_template('project/create_timeline.html',
                          title=f'إنشاء تايم لاين - {project.name}',
                          project=project)

@project_bp.route('/timeline/<int:timeline_id>/add_step', methods=['GET', 'POST'])
@login_required
def add_timeline_step(timeline_id):
    """إضافة خطوة جديدة للتايم لاين"""
    from app.models.timeline import ProjectTimeline, TimelineStep

    timeline = ProjectTimeline.query.get_or_404(timeline_id)
    project = timeline.project

    # Check permissions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id):
        flash('ليس لديك صلاحية لإضافة خطوات لهذا التايم لاين', 'danger')
        return redirect(url_for('project.timeline', id=project.id))

    if request.method == 'POST':
        source_type = request.form.get('source_type')
        title = request.form.get('title')
        description = request.form.get('description')
        task_id = request.form.get('task_id')
        links = request.form.get('links')

        if source_type not in ['task', 'manual']:
            flash('نوع مصدر الخطوة غير صحيح', 'danger')
            return redirect(url_for('project.add_timeline_step', timeline_id=timeline_id))

        # Get the next order index
        max_order = db.session.query(db.func.max(TimelineStep.order_index)).filter_by(timeline_id=timeline_id).scalar() or 0

        step = TimelineStep(
            timeline_id=timeline_id,
            source_type=source_type,
            created_by_id=current_user.id,
            order_index=max_order + 1
        )

        if source_type == 'task' and task_id:
            # خطوة مرتبطة بمهمة
            task = Task.query.get(task_id)
            if not task or task.project_id != project.id:
                flash('المهمة غير موجودة أو لا تنتمي لهذا المشروع', 'danger')
                return redirect(url_for('project.add_timeline_step', timeline_id=timeline_id))

            step.task_id = task_id
            # مزامنة البيانات من المهمة مباشرة
            step.title = task.title
            step.description = task.description
            step.status = task.status

        elif source_type == 'manual':
            # خطوة يدوية
            if not title:
                flash('عنوان الخطوة مطلوب للخطوات اليدوية', 'danger')
                return redirect(url_for('project.add_timeline_step', timeline_id=timeline_id))

            step.title = title
            step.description = description
            step.links = links

        db.session.add(step)
        db.session.commit()

        flash('تم إضافة الخطوة بنجاح', 'success')
        return redirect(url_for('project.timeline', id=project.id))

    # Get available tasks for this project
    available_tasks = Task.query.filter_by(project_id=project.id).all()

    return render_template('project/add_timeline_step.html',
                          title=f'إضافة خطوة - {timeline.name}',
                          timeline=timeline,
                          project=project,
                          available_tasks=available_tasks)

@project_bp.route('/timeline/step/<int:step_id>/update_status', methods=['POST'])
@login_required
def update_timeline_step_status(step_id):
    """تحديث حالة خطوة التايم لاين"""
    from app.models.timeline import TimelineStep

    step = TimelineStep.query.get_or_404(step_id)
    project = step.timeline.project

    # Check permissions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id or
            current_user in project.members):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لتحديث هذه الخطوة'}), 403

    # إذا كانت الخطوة مرتبطة بمهمة، لا يمكن تغيير حالتها يدوياً
    if step.source_type == 'task':
        return jsonify({'success': False, 'message': 'لا يمكن تغيير حالة خطوة مرتبطة بمهمة. يتم تحديث الحالة تلقائياً من المهمة.'}), 400

    new_status = request.form.get('status')
    valid_statuses = ['pending', 'in_progress', 'completed', 'cancelled']

    if new_status not in valid_statuses:
        return jsonify({'success': False, 'message': 'حالة غير صحيحة'}), 400

    try:
        step.status = new_status
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة الخطوة بنجاح',
            'status': step.get_status_display(),
            'color': step.get_status_color(),
            'icon': step.get_status_icon()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء تحديث الحالة: {str(e)}'
        }), 500

@project_bp.route('/timeline/step/<int:step_id>/delete', methods=['POST'])
@login_required
def delete_timeline_step(step_id):
    """حذف خطوة من التايم لاين"""
    from app.models.timeline import TimelineStep

    step = TimelineStep.query.get_or_404(step_id)
    project = step.timeline.project

    # Check permissions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id):
        flash('ليس لديك صلاحية لحذف خطوات من هذا التايم لاين', 'danger')
        return redirect(url_for('project.timeline', id=project.id))

    timeline_id = step.timeline_id
    db.session.delete(step)
    db.session.commit()

    flash('تم حذف الخطوة بنجاح', 'success')
    return redirect(url_for('project.timeline', id=project.id))

@project_bp.route('/timeline/<int:timeline_id>/sync', methods=['POST'])
@login_required
def sync_timeline_with_tasks(timeline_id):
    """مزامنة خطوات التايم لاين مع المهام"""
    from app.models.timeline import ProjectTimeline, TimelineStep

    timeline = ProjectTimeline.query.get_or_404(timeline_id)
    project = timeline.project

    # Check permissions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id):
        return jsonify({'success': False, 'message': 'ليس لديك صلاحية لمزامنة هذا التايم لاين'}), 403

    # مزامنة جميع الخطوات المرتبطة بالمهام
    synced_count = 0
    for step in timeline.steps.filter_by(source_type='task'):
        if step.task:
            # مزامنة البيانات من المهمة مباشرة
            step.title = step.task.title
            step.description = step.task.description
            step.status = step.task.status
            synced_count += 1

    db.session.commit()

    return jsonify({
        'success': True,
        'message': f'تم مزامنة {synced_count} خطوة مع المهام بنجاح'
    })

@project_bp.route('/timeline/step/<int:step_id>/manage')
@login_required
def manage_timeline_step(step_id):
    """صفحة إدارة خطوة التايم لاين"""
    from app.models.timeline import TimelineStep

    step = TimelineStep.query.get_or_404(step_id)
    project = step.timeline.project

    # Check permissions
    if not (current_user.has_role('admin') or current_user.has_role('manager') or
            current_user in project.managers or current_user.id == project.manager_id or
            current_user in project.members):
        flash('ليس لديك صلاحية لإدارة هذه الخطوة', 'danger')
        return redirect(url_for('project.timeline', id=project.id))

    return render_template('project/manage_timeline_step.html',
                          title=f'إدارة الخطوة - {step.title}',
                          step=step,
                          project=project,
                          timeline=step.timeline)
