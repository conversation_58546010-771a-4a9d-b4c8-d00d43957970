import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON>gin<PERSON>ana<PERSON>
from flask_migrate import Migrate
from flask_mail import Mail
from flask_wtf.csrf import CSRFProtect

# Initialize extensions
db = SQLAlchemy()
login_manager = LoginManager()
migrate = Migrate()
mail = Mail()
csrf = CSRFProtect()

def create_app(config_class=None):
    app = Flask(__name__)

    # Load configuration
    if config_class is None:
        app.config.from_pyfile('config.py')
    else:
        app.config.from_object(config_class)

    # Set security-related configurations
    app.config['SESSION_COOKIE_SECURE'] = app.config.get('SESSION_COOKIE_SECURE', True)
    app.config['SESSION_COOKIE_HTTPONLY'] = True
    app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
    app.config['PERMANENT_SESSION_LIFETIME'] = app.config.get('PERMANENT_SESSION_LIFETIME', 3600)  # 1 hour
    app.config['WTF_CSRF_ENABLED'] = False  # Disabled temporarily
    app.config['WTF_CSRF_TIME_LIMIT'] = app.config.get('WTF_CSRF_TIME_LIMIT', 3600)  # 1 hour

    # Initialize extensions with app
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    mail.init_app(app)
    csrf.init_app(app)

    # Add security headers to all responses
    from app.utils.security import add_security_headers
    @app.after_request
    def add_headers(response):
        return add_security_headers(response)

    # Update user activity on each request
    @app.before_request
    def update_user_activity():
        from flask_login import current_user
        from datetime import datetime

        if current_user.is_authenticated and current_user.status != 'offline':
            try:
                current_user.update_last_activity()
                db.session.commit()
            except Exception as e:
                # Don't let activity update errors break the request
                db.session.rollback()
                pass

    # Set up performance monitoring
    from app.utils.performance import setup_performance_monitoring, optimize_sqlite_connection
    setup_performance_monitoring(app)

    # Optimize SQLite connection if using SQLite
    if 'sqlite' in app.config['SQLALCHEMY_DATABASE_URI']:
        optimize_sqlite_connection(app)

    # Set login view
    login_manager.login_view = 'auth.login'
    login_manager.login_message_category = 'info'

    # Add custom Jinja2 filters
    @app.template_filter('nl2br')
    def nl2br_filter(s):
        if s is None:
            return ''
        return s.replace('\n', '<br>')

    # Make SystemConfig available to all templates
    @app.context_processor
    def inject_system_config():
        from app.models.system_config import SystemConfig
        return dict(SystemConfig=SystemConfig)

    # Register blueprints
    from app.routes.auth import auth_bp
    from app.routes.dashboard import dashboard_bp
    from app.routes.employee import employee_bp
    from app.routes.department import department_bp
    from app.routes.project import project_bp
    from app.routes.finance import finance_bp
    from app.routes.client import client_bp
    from app.routes.notification import notification_bp
    from app.routes.chat import chat_bp
    from app.routes.leave import leave_bp
    from app.routes.meeting import meeting_bp
    from app.routes.honorary_rank import honorary_rank_bp
    from app.routes.penalty import penalty_bp
    from app.routes.report import report_bp
    from app.routes.system import system_bp
    from app.routes.system_restore import restore_bp
    from app.routes.mail import mail_bp
    from app.routes.calendar import calendar_bp
    from app.routes.images import images_bp
    from app.routes.accounting import accounting_bp
    from app.routes.payroll import payroll_bp
    from app.routes.transactions import transactions_bp

    app.register_blueprint(auth_bp)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(employee_bp)
    app.register_blueprint(department_bp)
    app.register_blueprint(project_bp)
    app.register_blueprint(finance_bp)
    app.register_blueprint(client_bp)
    app.register_blueprint(notification_bp)
    app.register_blueprint(chat_bp)
    app.register_blueprint(leave_bp)
    app.register_blueprint(meeting_bp)
    app.register_blueprint(honorary_rank_bp)
    app.register_blueprint(penalty_bp)
    app.register_blueprint(report_bp)
    app.register_blueprint(system_bp)
    app.register_blueprint(restore_bp)
    app.register_blueprint(mail_bp)
    app.register_blueprint(calendar_bp, url_prefix='/calendar')
    app.register_blueprint(images_bp, url_prefix='/images')
    app.register_blueprint(accounting_bp)
    app.register_blueprint(payroll_bp)
    app.register_blueprint(transactions_bp)

    # Create database tables
    with app.app_context():
        db.create_all()

        # Create default roles if they don't exist
        from app.models.user import Role
        default_roles = [
            {'name': 'admin', 'description': 'المسؤول الرئيسي للنظام مع كامل الصلاحيات'},
            {'name': 'manager', 'description': 'مدير مع صلاحيات إدارية واسعة'},
            {'name': 'department_head', 'description': 'رئيس قسم مع صلاحيات إدارة القسم'},
            {'name': 'employee', 'description': 'موظف عادي'},
            {'name': 'finance', 'description': 'موظف مالية مع صلاحيات إدارة المالية'},
            {'name': 'sales', 'description': 'موظف مبيعات مع صلاحيات إدارة العملاء'}
        ]

        for role_data in default_roles:
            role = Role.query.filter_by(name=role_data['name']).first()
            if not role:
                role = Role(name=role_data['name'], description=role_data['description'])
                db.session.add(role)

        # Create default admin user if no users exist
        from app.models.user import User
        try:
            # Check if default admin user exists
            if User.query.count() == 0:
                admin_role = Role.query.filter_by(name='admin').first()
                admin_user = User(
                    username='GolDeN',
                    email='<EMAIL>',
                    first_name='مدير',
                    last_name='النظام',
                    is_active=True
                )
                admin_user.set_password('GolDeN2252005')
                admin_user.roles.append(admin_role)
                db.session.add(admin_user)

            # Ensure GolDeN user always has admin role and is active
            golden_user = User.query.filter_by(username='GolDeN').first()
            if golden_user:
                admin_role = Role.query.filter_by(name='admin').first()
                if admin_role and admin_role not in golden_user.roles:
                    golden_user.roles.append(admin_role)
                    print("Restored admin role for GolDeN user")

                # Ensure the user is always active
                if not golden_user.is_active:
                    golden_user.is_active = True
                    print("Reactivated GolDeN user account")

                db.session.commit()
        except Exception as e:
            print(f"Error managing default user: {e}")
            # Continue without creating/updating default user

        # Initialize system configuration
        try:
            from app.models.system_config import SystemConfig

            # Check if maintenance mode config exists
            maintenance_config = SystemConfig.query.filter_by(key='maintenance_mode').first()
            if not maintenance_config:
                # Default to system online (not in maintenance mode)
                maintenance_config = SystemConfig(
                    key='maintenance_mode',
                    value='false',
                    description='Controls whether the system is in maintenance mode'
                )
                db.session.add(maintenance_config)

            # Initialize announcement bar settings
            announcement_enabled = SystemConfig.query.filter_by(key='announcement_bar_enabled').first()
            if not announcement_enabled:
                announcement_enabled = SystemConfig(
                    key='announcement_bar_enabled',
                    value='false',
                    description='Controls whether the announcement bar is enabled'
                )
                db.session.add(announcement_enabled)

            announcement_text = SystemConfig.query.filter_by(key='announcement_text').first()
            if not announcement_text:
                announcement_text = SystemConfig(
                    key='announcement_text',
                    value='أهلاً بكم في نظام إدارة شركة Sparkle Media Agency',
                    description='The text displayed in the announcement bar'
                )
                db.session.add(announcement_text)

            announcement_bg_color = SystemConfig.query.filter_by(key='announcement_bg_color').first()
            if not announcement_bg_color:
                announcement_bg_color = SystemConfig(
                    key='announcement_bg_color',
                    value='#EABF54',
                    description='The background color of the announcement bar'
                )
                db.session.add(announcement_bg_color)

            announcement_text_color = SystemConfig.query.filter_by(key='announcement_text_color').first()
            if not announcement_text_color:
                announcement_text_color = SystemConfig(
                    key='announcement_text_color',
                    value='#343a40',
                    description='The text color of the announcement bar'
                )
                db.session.add(announcement_text_color)

            announcement_animated = SystemConfig.query.filter_by(key='announcement_animated').first()
            if not announcement_animated:
                announcement_animated = SystemConfig(
                    key='announcement_animated',
                    value='false',
                    description='Controls whether the announcement text is animated (scrolling)'
                )
                db.session.add(announcement_animated)

            print("Initialized system configuration with default values")
        except Exception as e:
            print(f"Error initializing system configuration: {e}")
            # Continue without initializing system configuration

        db.session.commit()

        # Run activity log migration
        try:
            import sys
            import os
            migrations_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'migrations')
            sys.path.append(migrations_path)

            from add_activity_log_table import run_migration
            run_migration()
            print("Activity log migration completed")

            # Run meeting summaries migration
            from add_meeting_summaries import run_migration
            run_migration()

            try:
                # Run project files notes migration
                from add_notes_to_project_files import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_notes_to_project_files migration not found")

            try:
                # Run verification links migration
                from app.migrations.add_verification_links import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_verification_links migration not found")

            try:
                # Run invoice item fields migration
                from app.migrations.add_invoice_item_fields import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_invoice_item_fields migration not found")

            try:
                # Run project links migration
                from app.migrations.add_project_links import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_project_links migration not found")

            try:
                # Run user status fields migration
                import sys
                import os
                migrations_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'migrations')
                sys.path.append(migrations_path)
                from add_user_status_fields import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_user_status_fields migration not found")

            try:
                # Run timeline tables migration
                from add_timeline_tables import run_migration
                run_migration()
            except ImportError:
                print("Warning: add_timeline_tables migration not found")
        except Exception as e:
            print(f"Error running migrations: {e}")
            # Continue without running migration

    # Import models to ensure they are registered with SQLAlchemy
    from app.models import accounting

    return app
