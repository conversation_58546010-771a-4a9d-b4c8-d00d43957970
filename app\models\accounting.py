"""
نماذج النظام المحاسبي
"""

from datetime import datetime
from app import db
from sqlalchemy import func, Numeric

class Account(db.Model):
    """نموذج شجرة الحسابات"""
    __tablename__ = 'accounts'
    
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(20), unique=True, nullable=False)  # رمز الحساب
    name = db.Column(db.String(100), nullable=False)  # اسم الحساب
    description = db.Column(db.Text)  # وصف الحساب
    account_type = db.Column(db.String(50), nullable=False)  # نوع الحساب
    parent_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # الحساب الأب
    balance = db.Column(Numeric(15, 2), default=0.00)  # رصيد الحساب
    is_active = db.Column(db.<PERSON>olean, default=True)  # حالة الحساب
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    parent = db.relationship('Account', remote_side=[id], backref='children')
    created_by = db.relationship('User', backref='created_accounts')
    
    # الفهارس
    __table_args__ = (
        db.Index('idx_account_code', 'code'),
        db.Index('idx_account_parent', 'parent_id'),
        db.Index('idx_account_type', 'account_type'),
    )
    
    def __repr__(self):
        return f'<Account {self.code}: {self.name}>'
    
    @property
    def full_name(self):
        """الحصول على الاسم الكامل للحساب مع التسلسل الهرمي"""
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name
    
    @property
    def level(self):
        """مستوى الحساب في الشجرة"""
        if self.parent:
            return self.parent.level + 1
        return 0
    
    @property
    def has_children(self):
        """هل للحساب حسابات فرعية"""
        return len(self.children) > 0
    
    def get_all_children(self):
        """الحصول على جميع الحسابات الفرعية (بما في ذلك الفرعية الفرعية)"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_all_children())
        return children
    
    def update_balance(self, amount, operation='add'):
        """تحديث رصيد الحساب"""
        if operation == 'add':
            self.balance += amount
        elif operation == 'subtract':
            self.balance -= amount
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def can_delete(self):
        """هل يمكن حذف الحساب"""
        # لا يمكن حذف الحساب إذا كان له حسابات فرعية أو معاملات
        if self.has_children:
            return False, "لا يمكن حذف حساب له حسابات فرعية"
        
        # فحص المعاملات المرتبطة
        from app.models.finance import TransactionItem, PayrollEntry
        
        if TransactionItem.query.filter_by(account_id=self.id).first():
            return False, "لا يمكن حذف حساب له معاملات مرتبطة"
        
        if PayrollEntry.query.filter(
            (PayrollEntry.debit_account_id == self.id) |
            (PayrollEntry.pending_account_id == self.id) |
            (PayrollEntry.paid_account_id == self.id)
        ).first():
            return False, "لا يمكن حذف حساب مرتبط برواتب"
        
        return True, "يمكن حذف الحساب"

class AccountType:
    """أنواع الحسابات"""
    ASSETS = 'assets'  # الأصول
    LIABILITIES = 'liabilities'  # الخصوم
    EQUITY = 'equity'  # حقوق الملكية
    REVENUE = 'revenue'  # الإيرادات
    EXPENSES = 'expenses'  # المصروفات
    CASH = 'cash'  # النقدية
    BANK = 'bank'  # البنوك
    RECEIVABLES = 'receivables'  # المدينون
    PAYABLES = 'payables'  # الدائنون
    INVENTORY = 'inventory'  # المخزون
    FIXED_ASSETS = 'fixed_assets'  # الأصول الثابتة
    OTHER = 'other'  # أخرى
    
    @classmethod
    def get_choices(cls):
        """الحصول على خيارات أنواع الحسابات"""
        return [
            (cls.CASH, 'النقدية'),
            (cls.BANK, 'البنوك'),
            (cls.RECEIVABLES, 'المدينون'),
            (cls.ASSETS, 'الأصول'),
            (cls.FIXED_ASSETS, 'الأصول الثابتة'),
            (cls.INVENTORY, 'المخزون'),
            (cls.LIABILITIES, 'الخصوم'),
            (cls.PAYABLES, 'الدائنون'),
            (cls.EQUITY, 'حقوق الملكية'),
            (cls.REVENUE, 'الإيرادات'),
            (cls.EXPENSES, 'المصروفات'),
            (cls.OTHER, 'أخرى'),
        ]
    
    @classmethod
    def get_display_name(cls, account_type):
        """الحصول على الاسم المعروض لنوع الحساب"""
        choices = dict(cls.get_choices())
        return choices.get(account_type, account_type)

class AccountingSettings(db.Model):
    """إعدادات النظام المحاسبي"""
    __tablename__ = 'accounting_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # إعدادات الرواتب
    payroll_debit_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب خصم الرواتب
    payroll_pending_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الرواتب المعلقة
    payroll_paid_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الرواتب المدفوعة
    
    # إعدادات الفواتير
    invoice_pending_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الفواتير المعلقة
    invoice_paid_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الفواتير المدفوعة
    invoice_overdue_account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'))  # حساب الفواتير المتأخرة
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # العلاقات
    payroll_debit_account = db.relationship('Account', foreign_keys=[payroll_debit_account_id])
    payroll_pending_account = db.relationship('Account', foreign_keys=[payroll_pending_account_id])
    payroll_paid_account = db.relationship('Account', foreign_keys=[payroll_paid_account_id])
    
    invoice_pending_account = db.relationship('Account', foreign_keys=[invoice_pending_account_id])
    invoice_paid_account = db.relationship('Account', foreign_keys=[invoice_paid_account_id])
    invoice_overdue_account = db.relationship('Account', foreign_keys=[invoice_overdue_account_id])
    
    created_by = db.relationship('User', backref='accounting_settings')
    
    @classmethod
    def get_settings(cls):
        """الحصول على الإعدادات الحالية أو إنشاء إعدادات جديدة"""
        settings = cls.query.first()
        if not settings:
            from flask_login import current_user
            settings = cls(created_by_id=current_user.id if current_user.is_authenticated else 1)
            db.session.add(settings)
            db.session.commit()
        return settings
