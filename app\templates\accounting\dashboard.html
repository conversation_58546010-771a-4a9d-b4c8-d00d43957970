{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line me-2"></i>
            لوحة تحكم النظام المحاسبي
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ url_for('accounting.accounts') }}" class="btn btn-primary">
                <i class="fas fa-sitemap me-1"></i>شجرة الحسابات
            </a>
            <a href="{{ url_for('payroll.index') }}" class="btn btn-success">
                <i class="fas fa-money-bill-wave me-1"></i>الرواتب
            </a>
            <a href="{{ url_for('accounting.settings') }}" class="btn btn-secondary">
                <i class="fas fa-cog me-1"></i>الإعدادات
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الحسابات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_accounts }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-sitemap fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                إجمالي الرواتب
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_payrolls }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                إجمالي المعاملات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                الرصيد الإجمالي
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ "%.2f"|format(main_accounts|sum(attribute='balance')) }} ر.س
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Accounts -->
        <div class="col-xl-6 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">الحسابات الرئيسية</h6>
                    <a href="{{ url_for('accounting.accounts') }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-eye me-1"></i>عرض الكل
                    </a>
                </div>
                <div class="card-body">
                    {% if main_accounts %}
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>رمز الحساب</th>
                                    <th>اسم الحساب</th>
                                    <th>النوع</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in main_accounts %}
                                <tr>
                                    <td>{{ account.code }}</td>
                                    <td>{{ account.name }}</td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ account.account_type }}
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <span class="fw-bold {{ 'text-success' if account.balance >= 0 else 'text-danger' }}">
                                            {{ "%.2f"|format(account.balance) }} ر.س
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-sitemap fa-3x text-gray-300 mb-3"></i>
                        <p class="text-muted">لا توجد حسابات رئيسية</p>
                        <a href="{{ url_for('accounting.create_account') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>إنشاء حساب جديد
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-xl-6 col-lg-5">
            <!-- Recent Transactions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">آخر المعاملات</h6>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    {% for transaction in recent_transactions %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="icon-circle bg-{{ transaction.status_color }}">
                                <i class="fas fa-exchange-alt text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ transaction.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="fw-bold">{{ transaction.name }}</div>
                            <div class="small">{{ transaction.status_display }}</div>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">{{ "%.2f"|format(transaction.total_amount) }} ر.س</div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-exchange-alt fa-2x text-gray-300 mb-2"></i>
                        <p class="text-muted small">لا توجد معاملات حديثة</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Payrolls -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">آخر الرواتب</h6>
                </div>
                <div class="card-body">
                    {% if recent_payrolls %}
                    {% for payroll in recent_payrolls %}
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            <div class="icon-circle bg-{{ payroll.status_color }}">
                                <i class="fas fa-money-bill-wave text-white"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <div class="small text-gray-500">{{ payroll.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="fw-bold">{{ payroll.employee_name }}</div>
                            <div class="small">{{ payroll.status_display }}</div>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">{{ "%.2f"|format(payroll.amount) }} ر.س</div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300 mb-2"></i>
                        <p class="text-muted small">لا توجد رواتب حديثة</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.icon-circle {
    height: 2.5rem;
    width: 2.5rem;
    border-radius: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
{% endblock %}
