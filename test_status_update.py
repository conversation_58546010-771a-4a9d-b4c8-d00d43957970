#!/usr/bin/env python3
"""
Script to test status update functionality
"""

from app import create_app, db
from app.models.user import User
from sqlalchemy import text

def test_status_functionality():
    """Test status update functionality"""
    try:
        print("Testing status update functionality...")
        
        # Get first user
        user = User.query.first()
        if not user:
            print("No users found in database")
            return False
        
        print(f"Testing with user: {user.username}")
        
        # Test current status
        print(f"Current status: {user.status}")
        print(f"Current custom_status: {user.custom_status}")
        print(f"Current last_activity: {user.last_activity}")
        
        # Test status display methods
        try:
            status_display = user.get_status_display()
            print(f"Status display: {status_display}")
        except Exception as e:
            print(f"Error in get_status_display: {e}")
            return False
        
        try:
            status_color = user.get_status_color()
            print(f"Status color: {status_color}")
        except Exception as e:
            print(f"Error in get_status_color: {e}")
            return False
        
        # Test set_status method
        try:
            user.set_status('busy', 'Testing status update')
            db.session.commit()
            print("Status update successful")
            
            # Verify update
            print(f"New status: {user.status}")
            print(f"New custom_status: {user.custom_status}")
            print(f"New status display: {user.get_status_display()}")
            
        except Exception as e:
            print(f"Error in set_status: {e}")
            db.session.rollback()
            return False
        
        # Reset status
        user.set_status('online', None)
        db.session.commit()
        print("Status reset to online")
        
        print("✅ All status functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in status functionality test: {e}")
        db.session.rollback()
        return False

def test_task_functionality():
    """Test task status functionality"""
    try:
        print("\nTesting task status functionality...")
        
        from app.models.task import Task
        
        # Get first task
        task = Task.query.first()
        if not task:
            print("No tasks found in database")
            return True  # Not an error if no tasks exist
        
        print(f"Testing with task: {task.title}")
        
        # Test task status methods
        try:
            status_display = task.get_status_display()
            print(f"Task status display: {status_display}")
        except Exception as e:
            print(f"Error in task get_status_display: {e}")
            return False
        
        try:
            status_color = task.get_status_color()
            print(f"Task status color: {status_color}")
        except Exception as e:
            print(f"Error in task get_status_color: {e}")
            return False
        
        try:
            assigned_to = task.assigned_to
            print(f"Task assigned to: {assigned_to.get_full_name() if assigned_to else 'None'}")
        except Exception as e:
            print(f"Error in task assigned_to: {e}")
            return False
        
        print("✅ All task functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error in task functionality test: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Testing status and task functionality...")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        success1 = test_status_functionality()
        success2 = test_task_functionality()
        
        if success1 and success2:
            print("\n🎉 All tests passed successfully!")
            return True
        else:
            print("\n💥 Some tests failed!")
            return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
