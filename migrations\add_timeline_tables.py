"""
Migration to create timeline tables
"""

from app import db
from sqlalchemy import text

def run_migration():
    """Create timeline tables"""
    try:
        # Create project_timelines table
        db.session.execute(text("""
            CREATE TABLE IF NOT EXISTS project_timelines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VA<PERSON>HA<PERSON>(255) NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                order_index INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                project_id INTEGER NOT NULL,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
        """))
        print("Created project_timelines table")

        # Create timeline_steps table
        db.session.execute(text("""
            CREATE TABLE IF NOT EXISTS timeline_steps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title VARCHAR(255) NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                order_index INTEGER DEFAULT 0,
                source_type VARCHAR(20) NOT NULL,
                links TEXT,
                task_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                timeline_id INTEGER NOT NULL,
                created_by_id INTEGER NOT NULL,
                FOREIGN KEY (timeline_id) REFERENCES project_timelines (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id),
                FOREIGN KEY (task_id) REFERENCES tasks (id)
            )
        """))
        print("Created timeline_steps table")

        # Create indexes for better performance
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_project_timelines_project_id 
            ON project_timelines (project_id)
        """))
        
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_timeline_steps_timeline_id 
            ON timeline_steps (timeline_id)
        """))
        
        db.session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_timeline_steps_task_id 
            ON timeline_steps (task_id)
        """))
        
        db.session.commit()
        print("Timeline tables migration completed successfully")
        
    except Exception as e:
        print(f"Error in timeline tables migration: {e}")
        db.session.rollback()

if __name__ == '__main__':
    run_migration()
