#!/usr/bin/env python3
"""
Script to update all users to simplified status system
"""

from app import create_app, db
from app.models.user import User
from sqlalchemy import text

def update_user_status():
    """Update all users to simplified status system"""
    try:
        print("Updating user status to simplified system...")
        
        # Update all users to have only online/offline status
        users = User.query.all()
        
        for user in users:
            # Convert old statuses to new system
            if user.status in ['busy', 'away']:
                user.status = 'online'  # Convert busy/away to online
            elif user.status not in ['online', 'offline']:
                user.status = 'offline'  # Default to offline for unknown statuses
            
            # Clear custom status
            user.custom_status = None
            
            print(f"Updated user {user.username}: status={user.status}")
        
        db.session.commit()
        
        print(f"✅ Updated {len(users)} users successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error updating user status: {e}")
        db.session.rollback()
        return False

def main():
    """Main function"""
    print("🔧 Updating user status system...")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        success = update_user_status()
        
        if success:
            print("\n🎉 User status update completed successfully!")
            return True
        else:
            print("\n💥 User status update failed!")
            return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
