{% extends 'base.html' %}

{% block content %}
<style>
.custom-dropdown {
    position: relative;
}

.custom-dropdown .dropdown-search {
    cursor: pointer;
    padding-right: 40px;
}

.custom-dropdown .dropdown-arrow {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6c757d;
}

.custom-dropdown .dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    max-height: 250px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.custom-dropdown.open .dropdown-list {
    display: block;
}

.custom-dropdown .dropdown-option {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
}

.custom-dropdown .dropdown-option:hover {
    background-color: #f8f9fa;
}

.custom-dropdown .dropdown-option.selected {
    background-color: #e3f2fd;
    color: #1976d2;
}

.custom-dropdown .dropdown-option:last-child {
    border-bottom: none;
}

.custom-dropdown .dropdown-option.hidden {
    display: none !important;
}

.search-highlight {
    background-color: #fff3cd;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
}

.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="custom-dropdown" data-target="related_project_id">
                                    <input type="text" class="form-control dropdown-search" placeholder="ابحث في المشاريع أو اختر..." autocomplete="off" readonly>
                                    <input type="hidden" id="related_project_id" name="related_project_id" value="">
                                    <div class="dropdown-arrow">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="dropdown-list">
                                        <div class="dropdown-option" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مشروع --
                                        </div>
                                        {% for project in projects %}
                                        <div class="dropdown-option" data-value="{{ project.id }}" data-text="{{ project.name }}" data-keywords="{{ project.name|lower }} {{ project.description|lower if project.description else '' }}">
                                            <i class="fas fa-project-diagram me-2 text-primary"></i>
                                            <div>
                                                <div class="fw-bold">{{ project.name }}</div>
                                                {% if project.description %}
                                                <small class="text-muted">{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="custom-dropdown" data-target="related_task_id">
                                    <input type="text" class="form-control dropdown-search" placeholder="ابحث في المهام أو اختر..." autocomplete="off" readonly>
                                    <input type="hidden" id="related_task_id" name="related_task_id" value="">
                                    <div class="dropdown-arrow">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="dropdown-list">
                                        <div class="dropdown-option" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مهمة --
                                        </div>
                                        {% for task in tasks %}
                                        <div class="dropdown-option" data-value="{{ task.id }}" data-text="{{ task.title }}" data-keywords="{{ task.title|lower }} {{ task.project.name|lower if task.project else '' }} {{ task.assigned_to.get_full_name()|lower if task.assigned_to else '' }}">
                                            <i class="fas fa-tasks me-2 text-info"></i>
                                            <div>
                                                <div class="fw-bold">{{ task.title }}</div>
                                                <small class="text-muted">
                                                    {% if task.project %}المشروع: {{ task.project.name }}{% endif %}
                                                    {% if task.assigned_to %} | المكلف: {{ task.assigned_to.get_full_name() }}{% endif %}
                                                </small>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // Initialize custom dropdowns
        initializeCustomDropdowns();
    });

    function initializeCustomDropdowns() {
        const dropdowns = document.querySelectorAll('.custom-dropdown');

        dropdowns.forEach(dropdown => {
            const searchInput = dropdown.querySelector('.dropdown-search');
            const hiddenInput = dropdown.querySelector('input[type="hidden"]');
            const dropdownList = dropdown.querySelector('.dropdown-list');
            const options = dropdown.querySelectorAll('.dropdown-option');
            const arrow = dropdown.querySelector('.dropdown-arrow i');

            console.log(`Initializing dropdown for ${hiddenInput.id}`);

            // Store original options
            const originalOptions = Array.from(options).map(option => ({
                element: option,
                value: option.getAttribute('data-value'),
                text: option.getAttribute('data-text') || option.textContent.trim(),
                keywords: option.getAttribute('data-keywords') || option.textContent.toLowerCase()
            }));

            console.log(`Found ${originalOptions.length} options`);

            // Toggle dropdown
            searchInput.addEventListener('click', function() {
                closeAllDropdowns();
                dropdown.classList.add('open');
                arrow.classList.remove('fa-chevron-down');
                arrow.classList.add('fa-chevron-up');
                this.removeAttribute('readonly');
                this.focus();
            });

            // Search functionality
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();
                console.log(`Searching for: "${searchTerm}"`);

                let visibleCount = 0;
                originalOptions.forEach(optionData => {
                    const matches = searchTerm === '' || optionData.keywords.includes(searchTerm);

                    if (matches) {
                        optionData.element.classList.remove('hidden');
                        visibleCount++;

                        // Highlight search term
                        if (searchTerm) {
                            highlightText(optionData.element, searchTerm);
                        } else {
                            removeHighlight(optionData.element);
                        }
                    } else {
                        optionData.element.classList.add('hidden');
                    }
                });

                console.log(`Showing ${visibleCount} options`);

                // Show "no results" message
                let noResultsMsg = dropdown.querySelector('.no-results');
                if (visibleCount === 0 && searchTerm !== '') {
                    if (!noResultsMsg) {
                        noResultsMsg = document.createElement('div');
                        noResultsMsg.className = 'dropdown-option no-results';
                        noResultsMsg.innerHTML = '<i class="fas fa-search me-2 text-muted"></i>لا توجد نتائج مطابقة للبحث';
                        noResultsMsg.style.fontStyle = 'italic';
                        dropdownList.appendChild(noResultsMsg);
                    }
                    noResultsMsg.style.display = 'flex';
                } else if (noResultsMsg) {
                    noResultsMsg.style.display = 'none';
                }
            });

            // Option selection
            options.forEach(option => {
                option.addEventListener('click', function() {
                    const value = this.getAttribute('data-value');
                    const text = this.getAttribute('data-text') || this.textContent.trim();

                    // Update values
                    hiddenInput.value = value;
                    searchInput.value = value ? text : '';
                    searchInput.setAttribute('readonly', 'readonly');

                    // Update selection visual
                    options.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');

                    // Close dropdown
                    dropdown.classList.remove('open');
                    arrow.classList.remove('fa-chevron-up');
                    arrow.classList.add('fa-chevron-down');

                    console.log(`Selected: ${text} (${value})`);
                });
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    dropdown.classList.remove('open');
                    arrow.classList.remove('fa-chevron-up');
                    arrow.classList.add('fa-chevron-down');
                    searchInput.setAttribute('readonly', 'readonly');
                }
            });

            // Escape key to close
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    dropdown.classList.remove('open');
                    arrow.classList.remove('fa-chevron-up');
                    arrow.classList.add('fa-chevron-down');
                    this.setAttribute('readonly', 'readonly');
                    this.blur();
                }
            });
        });
    }

    function closeAllDropdowns() {
        document.querySelectorAll('.custom-dropdown').forEach(dropdown => {
            dropdown.classList.remove('open');
            const arrow = dropdown.querySelector('.dropdown-arrow i');
            const searchInput = dropdown.querySelector('.dropdown-search');
            arrow.classList.remove('fa-chevron-up');
            arrow.classList.add('fa-chevron-down');
            searchInput.setAttribute('readonly', 'readonly');
        });
    }

    function highlightText(element, searchTerm) {
        const textElements = element.querySelectorAll('.fw-bold, small');
        textElements.forEach(textEl => {
            const originalText = textEl.getAttribute('data-original') || textEl.textContent;
            if (!textEl.getAttribute('data-original')) {
                textEl.setAttribute('data-original', originalText);
            }

            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
            textEl.innerHTML = highlightedText;
        });
    }

    function removeHighlight(element) {
        const textElements = element.querySelectorAll('.fw-bold, small');
        textElements.forEach(textEl => {
            const originalText = textEl.getAttribute('data-original');
            if (originalText) {
                textEl.textContent = originalText;
            }
        });
    }
</script>
{% endblock %}
{% endblock %}
