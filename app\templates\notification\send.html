{% extends 'base.html' %}

{% block content %}
<style>
.searchable-select {
    position: relative;
}

.searchable-select .search-input {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.searchable-select .dropdown-toggle {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
}

.searchable-select .search-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    width: 100%;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    background: white;
}

.searchable-select .dropdown-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
    padding: 8px 16px;
}

.searchable-select .dropdown-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.searchable-select .dropdown-item.active {
    background-color: #007bff;
    color: white;
}

.searchable-select .dropdown-item.d-none {
    display: none !important;
}

.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: bold;
}

.searchable-select .search-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.searchable-select .dropdown-menu.show {
    display: block;
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="searchable-select">
                                    <div class="input-group">
                                        <input type="text" class="form-control search-input" id="project_search" placeholder="ابحث في المشاريع..." autocomplete="off">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <select class="form-select d-none" id="related_project_id" name="related_project_id">
                                        <option value="">-- بدون مشروع --</option>
                                        {% for project in projects %}
                                        <option value="{{ project.id }}" data-keywords="{{ project.name }} {{ project.description or '' }}">{{ project.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <ul class="dropdown-menu w-100 search-dropdown" style="max-height: 250px; overflow-y: auto;">
                                        <li><a class="dropdown-item" href="#" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مشروع --
                                        </a></li>
                                        {% for project in projects %}
                                        <li><a class="dropdown-item" href="#" data-value="{{ project.id }}" data-keywords="{{ project.name|lower }} {{ project.description|lower if project.description else '' }}">
                                            <div class="d-flex align-items-start">
                                                <i class="fas fa-project-diagram me-2 text-primary mt-1"></i>
                                                <div class="flex-grow-1">
                                                    <div class="fw-bold">{{ project.name }}</div>
                                                    {% if project.description %}
                                                    <small class="text-muted">{{ project.description[:60] }}{% if project.description|length > 60 %}...{% endif %}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="searchable-select">
                                    <div class="input-group">
                                        <input type="text" class="form-control search-input" id="task_search" placeholder="ابحث في المهام..." autocomplete="off">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                    <select class="form-select d-none" id="related_task_id" name="related_task_id">
                                        <option value="">-- بدون مهمة --</option>
                                        {% for task in tasks %}
                                        <option value="{{ task.id }}" data-keywords="{{ task.title }} {{ task.project.name if task.project else '' }} {{ task.assigned_to.get_full_name() if task.assigned_to else '' }}">{{ task.title }}</option>
                                        {% endfor %}
                                    </select>
                                    <ul class="dropdown-menu w-100 search-dropdown" style="max-height: 250px; overflow-y: auto;">
                                        <li><a class="dropdown-item" href="#" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مهمة --
                                        </a></li>
                                        {% for task in tasks %}
                                        <li><a class="dropdown-item" href="#" data-value="{{ task.id }}" data-keywords="{{ task.title|lower }} {{ task.project.name|lower if task.project else '' }} {{ task.assigned_to.get_full_name()|lower if task.assigned_to else '' }}">
                                            <div class="d-flex align-items-start">
                                                <i class="fas fa-tasks me-2 text-info mt-1"></i>
                                                <div class="flex-grow-1">
                                                    <div class="fw-bold">{{ task.title }}</div>
                                                    <small class="text-muted">
                                                        {% if task.project %}المشروع: {{ task.project.name }}{% endif %}
                                                        {% if task.assigned_to %} | المكلف: {{ task.assigned_to.get_full_name() }}{% endif %}
                                                    </small>
                                                </div>
                                            </div>
                                        </a></li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // Initialize searchable dropdowns
        initializeSearchableDropdown('project_search', 'related_project_id');
        initializeSearchableDropdown('task_search', 'related_task_id');
    });

    function initializeSearchableDropdown(searchInputId, selectId) {
        const searchInput = document.getElementById(searchInputId);
        const select = document.getElementById(selectId);
        const dropdown = searchInput.closest('.searchable-select').querySelector('.search-dropdown');
        const dropdownItems = dropdown.querySelectorAll('.dropdown-item');
        const dropdownToggle = searchInput.closest('.searchable-select').querySelector('.dropdown-toggle');

        // Show dropdown when clicking toggle button or focusing on search input
        dropdownToggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleDropdown();
        });

        searchInput.addEventListener('focus', function() {
            showDropdown();
        });

        // Filter items as user types
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterItems(searchTerm);
            showDropdown();
        });

        // Handle item selection
        dropdown.addEventListener('click', function(e) {
            e.preventDefault();
            const item = e.target.closest('.dropdown-item');
            if (item) {
                const value = item.getAttribute('data-value');
                const text = item.querySelector('.fw-bold') ? item.querySelector('.fw-bold').textContent : item.textContent.trim();

                // Update select value
                select.value = value;

                // Update search input
                searchInput.value = value ? text : '';

                // Hide dropdown
                hideDropdown();

                // Clear search filter
                filterItems('');
            }
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.closest('.searchable-select').contains(e.target)) {
                hideDropdown();
            }
        });

        // Clear search when input is cleared
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Escape') {
                hideDropdown();
            }
            if (this.value === '') {
                select.value = '';
                filterItems('');
            }
        });

        function showDropdown() {
            dropdown.classList.add('show');
        }

        function hideDropdown() {
            dropdown.classList.remove('show');
        }

        function toggleDropdown() {
            if (dropdown.classList.contains('show')) {
                hideDropdown();
            } else {
                showDropdown();
                searchInput.focus();
            }
        }

        function filterItems(searchTerm) {
            dropdownItems.forEach(item => {
                const keywords = item.getAttribute('data-keywords') || '';
                const text = item.textContent.toLowerCase();

                const matches = keywords.includes(searchTerm) || text.includes(searchTerm);

                if (matches) {
                    item.classList.remove('d-none');
                    // Highlight search term
                    highlightSearchTerm(item, searchTerm);
                } else {
                    item.classList.add('d-none');
                }
            });
        }

        function highlightSearchTerm(item, searchTerm) {
            if (!searchTerm) return;

            const textElements = item.querySelectorAll('.fw-bold, small');
            textElements.forEach(element => {
                const originalText = element.getAttribute('data-original') || element.textContent;
                if (!element.getAttribute('data-original')) {
                    element.setAttribute('data-original', originalText);
                }

                if (searchTerm.length > 0) {
                    const regex = new RegExp(`(${searchTerm})`, 'gi');
                    const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');
                    element.innerHTML = highlightedText;
                } else {
                    element.textContent = originalText;
                }
            });
        }
    }
</script>
{% endblock %}
{% endblock %}
