{% extends 'base.html' %}

{% block content %}
<style>
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
}

.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select option {
    padding: 8px;
}

.form-select option:hover {
    background-color: #f8f9fa;
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="project_search" placeholder="ابحث في المشاريع أو اختر من القائمة..." autocomplete="off">
                                    <select class="form-select mt-2" id="related_project_id" name="related_project_id">
                                        <option value="">-- بدون مشروع --</option>
                                        {% for project in projects %}
                                        <option value="{{ project.id }}" data-name="{{ project.name }}" data-description="{{ project.description or '' }}">
                                            {{ project.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="task_search" placeholder="ابحث في المهام أو اختر من القائمة..." autocomplete="off">
                                    <select class="form-select mt-2" id="related_task_id" name="related_task_id">
                                        <option value="">-- بدون مهمة --</option>
                                        {% for task in tasks %}
                                        <option value="{{ task.id }}" data-name="{{ task.title }}" data-project="{{ task.project.name if task.project else '' }}" data-assignee="{{ task.assigned_to.get_full_name() if task.assigned_to else '' }}">
                                            {{ task.title }}{% if task.project %} - {{ task.project.name }}{% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // Initialize search functionality for projects and tasks
        setupSearchableSelect('project_search', 'related_project_id');
        setupSearchableSelect('task_search', 'related_task_id');
    });

    function setupSearchableSelect(searchInputId, selectId) {
        const searchInput = document.getElementById(searchInputId);
        const select = document.getElementById(selectId);
        const options = Array.from(select.options);

        // Store original options
        const originalOptions = options.map(option => ({
            value: option.value,
            text: option.textContent,
            name: option.getAttribute('data-name') || '',
            description: option.getAttribute('data-description') || '',
            project: option.getAttribute('data-project') || '',
            assignee: option.getAttribute('data-assignee') || ''
        }));

        // Filter options based on search input
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            filterSelectOptions(searchTerm);
        });

        // Update search input when select changes
        select.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const name = selectedOption.getAttribute('data-name') || selectedOption.textContent;
                searchInput.value = name;
            } else {
                searchInput.value = '';
            }
        });

        // Clear search when input is cleared
        searchInput.addEventListener('keyup', function(e) {
            if (this.value === '') {
                select.value = '';
                showAllOptions();
            }
        });

        function filterSelectOptions(searchTerm) {
            // Clear current options (except first one)
            while (select.options.length > 1) {
                select.removeChild(select.options[1]);
            }

            if (!searchTerm) {
                showAllOptions();
                return;
            }

            // Filter and add matching options
            originalOptions.forEach(optionData => {
                if (optionData.value === '') return; // Skip empty option

                const searchableText = `${optionData.text} ${optionData.name} ${optionData.description} ${optionData.project} ${optionData.assignee}`.toLowerCase();

                if (searchableText.includes(searchTerm)) {
                    const option = document.createElement('option');
                    option.value = optionData.value;
                    option.textContent = optionData.text;
                    option.setAttribute('data-name', optionData.name);
                    if (optionData.description) option.setAttribute('data-description', optionData.description);
                    if (optionData.project) option.setAttribute('data-project', optionData.project);
                    if (optionData.assignee) option.setAttribute('data-assignee', optionData.assignee);

                    // Highlight search term in option text
                    if (searchTerm.length > 0) {
                        const regex = new RegExp(`(${searchTerm})`, 'gi');
                        const highlightedText = optionData.text.replace(regex, '★$1★');
                        option.textContent = highlightedText;
                    }

                    select.appendChild(option);
                }
            });

            // If no matches found, show message
            if (select.options.length === 1) {
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'لا توجد نتائج مطابقة للبحث';
                option.disabled = true;
                select.appendChild(option);
            }
        }

        function showAllOptions() {
            // Clear current options (except first one)
            while (select.options.length > 1) {
                select.removeChild(select.options[1]);
            }

            // Add all original options
            originalOptions.forEach(optionData => {
                if (optionData.value === '') return; // Skip empty option

                const option = document.createElement('option');
                option.value = optionData.value;
                option.textContent = optionData.text;
                option.setAttribute('data-name', optionData.name);
                if (optionData.description) option.setAttribute('data-description', optionData.description);
                if (optionData.project) option.setAttribute('data-project', optionData.project);
                if (optionData.assignee) option.setAttribute('data-assignee', optionData.assignee);

                select.appendChild(option);
            });
        }
    }
</script>
{% endblock %}
{% endblock %}
