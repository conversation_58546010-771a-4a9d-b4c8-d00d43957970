{% extends 'base.html' %}

{% block content %}
<style>
.dropdown-item {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #007bff;
    color: white;
}

.position-relative .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="project_search" placeholder="ابحث عن مشروع..." autocomplete="off">
                                    <input type="hidden" id="related_project_id" name="related_project_id" value="">
                                    <div id="project_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                                        <div class="dropdown-item" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مشروع --
                                        </div>
                                        {% for project in projects %}
                                        <div class="dropdown-item" data-value="{{ project.id }}" data-name="{{ project.name }}">
                                            <i class="fas fa-project-diagram me-2 text-primary"></i>{{ project.name }}
                                            <small class="text-muted d-block">{{ project.description[:50] if project.description else 'لا يوجد وصف' }}{% if project.description and project.description|length > 50 %}...{% endif %}</small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="task_search" placeholder="ابحث عن مهمة..." autocomplete="off">
                                    <input type="hidden" id="related_task_id" name="related_task_id" value="">
                                    <div id="task_dropdown" class="dropdown-menu w-100" style="max-height: 200px; overflow-y: auto; display: none;">
                                        <div class="dropdown-item" data-value="">
                                            <i class="fas fa-times me-2 text-muted"></i>-- بدون مهمة --
                                        </div>
                                        {% for task in tasks %}
                                        <div class="dropdown-item" data-value="{{ task.id }}" data-name="{{ task.title }}" data-project="{{ task.project.name if task.project else '' }}">
                                            <i class="fas fa-tasks me-2 text-info"></i>{{ task.title }}
                                            <small class="text-muted d-block">
                                                {% if task.project %}المشروع: {{ task.project.name }}{% endif %}
                                                {% if task.assigned_to %} | المكلف: {{ task.assigned_to.get_full_name() }}{% endif %}
                                            </small>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // Project search functionality
        setupSearchDropdown('project_search', 'project_dropdown', 'related_project_id');

        // Task search functionality
        setupSearchDropdown('task_search', 'task_dropdown', 'related_task_id');
    });

    function setupSearchDropdown(searchInputId, dropdownId, hiddenInputId) {
        const searchInput = document.getElementById(searchInputId);
        const dropdown = document.getElementById(dropdownId);
        const hiddenInput = document.getElementById(hiddenInputId);
        const dropdownItems = dropdown.querySelectorAll('.dropdown-item');

        // Show dropdown on focus
        searchInput.addEventListener('focus', function() {
            dropdown.style.display = 'block';
            filterDropdownItems('');
        });

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchInput.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });

        // Filter items on input
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterDropdownItems(searchTerm);
        });

        // Handle item selection
        dropdown.addEventListener('click', function(e) {
            if (e.target.classList.contains('dropdown-item') || e.target.closest('.dropdown-item')) {
                const item = e.target.classList.contains('dropdown-item') ? e.target : e.target.closest('.dropdown-item');
                const value = item.getAttribute('data-value');
                const name = item.getAttribute('data-name') || item.textContent.trim();

                hiddenInput.value = value;
                searchInput.value = value ? name : '';
                dropdown.style.display = 'none';

                // If this is task search and a project is selected, filter tasks
                if (searchInputId === 'task_search') {
                    filterTasksByProject();
                }
            }
        });

        function filterDropdownItems(searchTerm) {
            dropdownItems.forEach(item => {
                const text = item.textContent.toLowerCase();
                const name = item.getAttribute('data-name') || '';
                const project = item.getAttribute('data-project') || '';

                const matches = text.includes(searchTerm) ||
                               name.toLowerCase().includes(searchTerm) ||
                               project.toLowerCase().includes(searchTerm);

                item.style.display = matches ? 'block' : 'none';
            });
        }

        function filterTasksByProject() {
            const selectedProjectId = document.getElementById('related_project_id').value;
            if (!selectedProjectId || dropdownId !== 'task_dropdown') return;

            // This would require additional data attributes or AJAX call
            // For now, we'll keep all tasks visible
        }
    }
</script>
{% endblock %}
{% endblock %}
