{% extends 'base.html' %}

{% block content %}
<style>
.search-highlight {
    background-color: #fff3cd;
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: bold;
}

.form-control:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-select option {
    padding: 8px;
}

.form-select option:hover {
    background-color: #f8f9fa;
}

/* Hide options that don't match search */
.form-select option[style*="display: none"] {
    display: none !important;
}

/* Search input styling */
.form-control[placeholder*="ابحث"] {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom: 1px solid #dee2e6;
}

.form-select.mt-2 {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border-top: none;
}
</style>
<div class="container">
    <div class="row mb-4">
        <div class="col">
            <h1>إرسال إشعار</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('dashboard.index') }}">الرئيسية</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('notification.index') }}">الإشعارات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">إرسال إشعار</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-light">
                    <h5 class="mb-0">إرسال إشعار جديد</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('notification.send') }}" method="POST">
                        <div class="mb-3">
                            <label for="user_ids" class="form-label">المستلمين</label>
                            <select class="form-select" id="user_ids" name="user_ids" multiple required>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }} ({{ user.email }})</option>
                                {% endfor %}
                            </select>
                            <div class="form-text">يمكنك اختيار أكثر من مستلم باستخدام مفتاح Ctrl أو Shift</div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select_all_users" name="select_all_users">
                                <label class="form-check-label" for="select_all_users">
                                    إرسال إلى جميع المستخدمين
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="title" class="form-label">العنوان</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="message" class="form-label">الرسالة</label>
                            <textarea class="form-control" id="message" name="message" rows="3" required></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="link_url" class="form-label">رابط (اختياري)</label>
                            <input type="url" class="form-control" id="link_url" name="link_url" placeholder="https://example.com">
                            <div class="form-text">يمكنك إضافة رابط للإشعار (مثل رابط لصفحة المشروع أو المهمة)</div>
                        </div>

                        <div class="mb-3">
                            <label for="link_text" class="form-label">نص الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="link_text" name="link_text" placeholder="انقر هنا للعرض">
                            <div class="form-text">النص الذي سيظهر للرابط</div>
                        </div>
                        <div class="mb-3">
                            <label for="notification_type" class="form-label">نوع الإشعار</label>
                            <select class="form-select" id="notification_type" name="notification_type">
                                <option value="">-- بدون نوع --</option>
                                <option value="task">مهمة</option>
                                <option value="project">مشروع</option>
                                <option value="system">النظام</option>
                                <option value="finance">مالية</option>
                                <option value="client">عميل</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="related_project_id" class="form-label">المشروع المرتبط (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="project_search" placeholder="ابحث في المشاريع أو اختر من القائمة..." autocomplete="off">
                                    <select class="form-select mt-2" id="related_project_id" name="related_project_id">
                                        <option value="">-- بدون مشروع --</option>
                                        {% for project in projects %}
                                        <option value="{{ project.id }}" data-name="{{ project.name }}" data-description="{{ project.description or '' }}">
                                            {{ project.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="related_task_id" class="form-label">المهمة المرتبطة (اختياري)</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="task_search" placeholder="ابحث في المهام أو اختر من القائمة..." autocomplete="off">
                                    <select class="form-select mt-2" id="related_task_id" name="related_task_id">
                                        <option value="">-- بدون مهمة --</option>
                                        {% for task in tasks %}
                                        <option value="{{ task.id }}" data-name="{{ task.title }}" data-project="{{ task.project.name if task.project else '' }}" data-assignee="{{ task.assigned_to.get_full_name() if task.assigned_to else '' }}">
                                            {{ task.title }}{% if task.project %} - {{ task.project.name }}{% endif %}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">إرسال الإشعار</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filter tasks based on selected project
        const projectSelect = document.getElementById('related_project_id');
        const taskSelect = document.getElementById('related_task_id');

        if (projectSelect && taskSelect) {
            projectSelect.addEventListener('change', function() {
                const projectId = this.value;

                // Reset task select
                taskSelect.innerHTML = '<option value="">-- بدون مهمة --</option>';

                if (projectId) {
                    // Filter tasks by project
                    {% for task in tasks %}
                    if ('{{ task.project_id }}' === projectId) {
                        const option = document.createElement('option');
                        option.value = '{{ task.id }}';
                        option.textContent = '{{ task.title }}';
                        taskSelect.appendChild(option);
                    }
                    {% endfor %}
                } else {
                    // Show all tasks
                    {% for task in tasks %}
                    const option = document.createElement('option');
                    option.value = '{{ task.id }}';
                    option.textContent = '{{ task.title }}';
                    taskSelect.appendChild(option);
                    {% endfor %}
                }
            });
        }

        // Handle select all users checkbox
        const selectAllCheckbox = document.getElementById('select_all_users');
        const userSelect = document.getElementById('user_ids');

        if (selectAllCheckbox && userSelect) {
            selectAllCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    // Select all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = true;
                    }
                    userSelect.disabled = true;
                } else {
                    // Deselect all options
                    for (let i = 0; i < userSelect.options.length; i++) {
                        userSelect.options[i].selected = false;
                    }
                    userSelect.disabled = false;
                }
            });
        }

        // Initialize search functionality
        initializeSearch();
    });

    function initializeSearch() {
        // Project search
        const projectSearch = document.getElementById('project_search');
        const projectSelect = document.getElementById('related_project_id');

        if (projectSearch && projectSelect) {
            setupSearch(projectSearch, projectSelect);
        }

        // Task search
        const taskSearch = document.getElementById('task_search');
        const taskSelect = document.getElementById('related_task_id');

        if (taskSearch && taskSelect) {
            setupSearch(taskSearch, taskSelect);
        }
    }

    function setupSearch(searchInput, selectElement) {
        // Store all original options
        const allOptions = Array.from(selectElement.options).map(option => ({
            value: option.value,
            text: option.textContent.trim(),
            element: option.cloneNode(true)
        }));

        console.log(`Stored ${allOptions.length} options for ${selectElement.id}`);

        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            console.log(`Searching for: "${searchTerm}"`);

            // Show/hide options based on search
            Array.from(selectElement.options).forEach(option => {
                if (option.value === '') {
                    // Always show the empty option
                    option.style.display = '';
                    return;
                }

                const optionText = option.textContent.toLowerCase();
                const dataName = option.getAttribute('data-name') || '';
                const dataDescription = option.getAttribute('data-description') || '';
                const dataProject = option.getAttribute('data-project') || '';
                const dataAssignee = option.getAttribute('data-assignee') || '';

                const searchableText = `${optionText} ${dataName} ${dataDescription} ${dataProject} ${dataAssignee}`.toLowerCase();

                if (searchTerm === '' || searchableText.includes(searchTerm)) {
                    option.style.display = '';
                } else {
                    option.style.display = 'none';
                }
            });
        });

        // Update search input when selection changes
        selectElement.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption && selectedOption.value) {
                const displayName = selectedOption.getAttribute('data-name') || selectedOption.textContent;
                searchInput.value = displayName;
            } else {
                searchInput.value = '';
            }
        });

        // Clear selection when search is cleared
        searchInput.addEventListener('keyup', function(e) {
            if (this.value.trim() === '') {
                selectElement.value = '';
                // Show all options
                Array.from(selectElement.options).forEach(option => {
                    option.style.display = '';
                });
            }
        });
    }
</script>
{% endblock %}
{% endblock %}
