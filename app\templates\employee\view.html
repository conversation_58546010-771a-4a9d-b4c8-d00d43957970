{% extends 'base.html' %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>بيانات الموظف</h1>
    <div>
        <a href="{{ url_for('employee.index') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
        </a>
        {% if current_user.has_role('admin') or current_user.has_role('manager') or
            current_user.id == employee.id %}
        <a href="{{ url_for('employee.edit', id=employee.id) }}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>تعديل
        </a>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card shadow">
            <div class="card-body text-center">
                {% if employee.has_profile_image_in_db() %}
                <img src="{{ url_for('images.user_profile_image', user_id=employee.id) }}" alt="{{ employee.get_full_name() }}" class="profile-image mb-3" style="width: 150px; height: 150px; object-fit: cover; border-radius: 50%;">
                {% elif employee.profile_image and employee.profile_image != 'default.jpg' %}
                <img src="{{ url_for('static', filename=employee.profile_image) }}" alt="{{ employee.get_full_name() }}" class="profile-image mb-3" style="width: 150px; height: 150px; object-fit: cover; border-radius: 50%;">
                {% else %}
                <img src="{{ url_for('static', filename='uploads/default.jpg') }}" alt="{{ employee.get_full_name() }}" class="profile-image mb-3" style="width: 150px; height: 150px; object-fit: cover; border-radius: 50%;">
                {% endif %}
                <h3>{{ employee.get_full_name() }}</h3>
                {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.id == employee.id %}
                    {% if employee.full_name_en %}
                    <p class="text-muted">{{ employee.full_name_en }}</p>
                    {% elif employee.first_name_en and employee.last_name_en %}
                    <p class="text-muted">{{ employee.first_name_en }} {{ employee.last_name_en }}</p>
                    {% endif %}
                {% elif employee.first_name_en and employee.last_name_en %}
                <p class="text-muted">{{ employee.first_name_en }} {{ employee.last_name_en }}</p>
                {% endif %}
                <p class="text-muted">{{ employee.username }}</p>

                <div class="d-flex justify-content-center mb-3">
                    {% for role in employee.roles %}
                    <span class="badge bg-primary me-1">{{ role.name }}</span>
                    {% endfor %}
                </div>

                <!-- Honorary Ranks -->
                {% if employee.honorary_ranks %}
                <div class="d-flex justify-content-center mb-3">
                    {% for rank in employee.honorary_ranks %}
                    <span class="badge me-1" style="background-color: {{ rank.color }}; color: {{ '#000' if rank.color == '#ffffff' else '#fff' }}">
                        <i class="fas {{ rank.icon }} me-1"></i>{{ rank.name }}
                    </span>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Connection Status -->
                <div class="mb-3">
                    <div class="d-flex justify-content-center align-items-center">
                        <i class="fas fa-circle me-2 text-{{ employee.get_status_color() }}" style="font-size: 0.8em;"></i>
                        <span class="text-{{ employee.get_status_color() }} fw-bold">{{ employee.get_status_display() }}</span>
                    </div>
                    {% if employee.last_activity %}
                    <small class="text-muted d-block mt-1">آخر نشاط: {{ employee.last_activity.strftime('%Y-%m-%d %H:%M') }}</small>
                    {% endif %}
                </div>

                <p class="mb-1">
                    <i class="fas fa-envelope me-2"></i>{{ employee.email }}
                </p>
                {% if employee.phone %}
                <p class="mb-1">
                    <i class="fas fa-phone me-2"></i>{{ employee.phone }}
                </p>
                {% endif %}
                <p class="mb-1">
                    <i class="fas fa-building me-2"></i>{{ employee.department.name if employee.department else 'غير محدد' }}
                </p>
                {% if employee.birth_date and (current_user.has_role('admin') or current_user.id == employee.id) %}
                <p class="mb-1">
                    <i class="fas fa-birthday-cake me-2"></i>تاريخ الميلاد: {{ employee.birth_date.strftime('%Y-%m-%d') }}
                </p>
                {% endif %}
                {% if employee.nationality %}
                <p class="mb-1">
                    <i class="fas fa-flag me-2"></i>الجنسية: {{ employee.nationality }}
                </p>
                {% endif %}
                <p class="mb-1">
                    <i class="fas fa-calendar-alt me-2"></i>تاريخ الانضمام: {{ employee.date_joined.strftime('%Y-%m-%d') }}
                </p>
                <p class="mb-1">
                    <i class="fas fa-sign-in-alt me-2"></i>آخر تسجيل دخول: {{ employee.last_login.strftime('%Y-%m-%d %H:%M') if employee.last_login else 'لم يسجل الدخول بعد' }}
                </p>

                <div class="mt-3">
                    {% if employee.is_active %}
                    <span class="badge bg-success">نشط</span>
                    {% else %}
                    <span class="badge bg-danger">غير نشط</span>
                    {% endif %}

                    {% if active_leave %}
                    <span class="badge bg-warning text-dark ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="من {{ active_leave.start_date.strftime('%Y-%m-%d') }} إلى {{ active_leave.end_date.strftime('%Y-%m-%d') }}">
                        <i class="fas fa-umbrella-beach me-1"></i>في إجازة
                    </span>
                    {% endif %}

                    {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        {% set has_active_penalty = false %}
                        {% for penalty in employee.penalties %}
                            {% if penalty.is_active() and not has_active_penalty %}
                            <span class="badge bg-danger ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ penalty.get_penalty_type_display() }} - {{ penalty.reason }}">
                                <i class="fas fa-exclamation-triangle me-1"></i>عقوبة سارية
                            </span>
                            {% set has_active_penalty = true %}
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="mt-3">
                    {% if current_user.id == employee.id or current_user.has_role('admin') %}
                    <a href="{{ url_for('employee.change_password', id=employee.id) }}" class="btn btn-warning btn-sm">
                        <i class="fas fa-key me-1"></i>تغيير كلمة المرور
                    </a>
                    {% endif %}

                    {% if current_user.has_role('admin') %}
                    <a href="{{ url_for('employee.view_password', id=employee.id) }}" class="btn btn-danger btn-sm ms-2">
                        <i class="fas fa-unlock-alt me-1"></i>إعادة تعيين كلمة المرور
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>

        {% if employee.bio %}
        <div class="card shadow mt-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">نبذة شخصية</h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ employee.bio }}</p>
            </div>
        </div>
        {% endif %}
    </div>

    <div class="col-md-8">
        <!-- Employee Statistics -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">إحصائيات الموظف</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 col-md-3 mb-3">
                        <div class="h3">{{ total_projects }}</div>
                        <div class="text-muted">إجمالي المشاريع</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="h3">{{ completed_projects }}</div>
                        <div class="text-muted">المشاريع المكتملة</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="h3">{{ in_progress_projects }}</div>
                        <div class="text-muted">المشاريع الجارية</div>
                    </div>
                    <div class="col-6 col-md-3 mb-3">
                        <div class="h3">{{ pending_projects }}</div>
                        <div class="text-muted">المشاريع المعلقة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المشاريع المشارك فيها</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#projectsFilterCollapse" aria-expanded="false" aria-controls="projectsFilterCollapse">
                        <i class="fas fa-filter me-1"></i>تصفية
                    </button>
                </div>
            </div>

            <!-- Projects Filter -->
            <div class="collapse" id="projectsFilterCollapse">
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ url_for('employee.view', id=employee.id) }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="projects_status" class="form-label">الحالة</label>
                            <select class="form-select" id="projects_status" name="projects_status">
                                <option value="all" {% if projects_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                <option value="pending" {% if projects_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="in_progress" {% if projects_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                <option value="completed" {% if projects_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                <option value="cancelled" {% if projects_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="projects_per_page" class="form-label">عدد النتائج</label>
                            <select class="form-select" id="projects_per_page" name="projects_per_page">
                                <option value="10" {% if projects_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if projects_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if projects_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if projects_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">تطبيق</button>
                            <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card-body">
                {% if projects.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>اسم المشروع</th>
                                <th>الحالة</th>
                                <th>تاريخ البدء</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for project in projects.items %}
                            <tr>
                                <td>{{ project.name }}</td>
                                <td>
                                    {% if project.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif project.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif project.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif project.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>{{ project.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ project.end_date.strftime('%Y-%m-%d') if project.end_date else 'غير محدد' }}</td>
                                <td>
                                    <a href="{{ url_for('project.view', id=project.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <!-- Projects Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if projects.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employee.view', id=employee.id, projects_page=projects.prev_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=tasks_page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in projects.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == projects.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employee.view', id=employee.id, projects_page=page_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=tasks_page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if projects.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employee.view', id=employee.id, projects_page=projects.next_num, projects_per_page=projects_per_page, projects_status=projects_status_filter, tasks_page=tasks_page, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد مشاريع مشارك فيها حاليًا.</p>
                {% endif %}
            </div>
        </div>

        <!-- Tasks -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">المهام المسندة</h5>
                <div>
                    <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#tasksFilterCollapse" aria-expanded="false" aria-controls="tasksFilterCollapse">
                        <i class="fas fa-filter me-1"></i>تصفية
                    </button>
                </div>
            </div>

            <!-- Tasks Filter -->
            <div class="collapse" id="tasksFilterCollapse">
                <div class="card-body border-bottom">
                    <form method="GET" action="{{ url_for('employee.view', id=employee.id) }}" class="row g-3">
                        <!-- Keep existing project filters -->
                        <input type="hidden" name="projects_page" value="{{ request.args.get('projects_page', 1) }}">
                        <input type="hidden" name="projects_per_page" value="{{ projects_per_page }}">
                        <input type="hidden" name="projects_status" value="{{ projects_status_filter }}">

                        <div class="col-md-4">
                            <label for="tasks_status" class="form-label">الحالة</label>
                            <select class="form-select" id="tasks_status" name="tasks_status">
                                <option value="all" {% if tasks_status_filter == 'all' %}selected{% endif %}>الكل</option>
                                <option value="pending" {% if tasks_status_filter == 'pending' %}selected{% endif %}>معلق</option>
                                <option value="in_progress" {% if tasks_status_filter == 'in_progress' %}selected{% endif %}>قيد التنفيذ</option>
                                <option value="completed" {% if tasks_status_filter == 'completed' %}selected{% endif %}>مكتمل</option>
                                <option value="cancelled" {% if tasks_status_filter == 'cancelled' %}selected{% endif %}>ملغي</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tasks_priority" class="form-label">الأولوية</label>
                            <select class="form-select" id="tasks_priority" name="tasks_priority">
                                <option value="all" {% if tasks_priority_filter == 'all' %}selected{% endif %}>الكل</option>
                                <option value="low" {% if tasks_priority_filter == 'low' %}selected{% endif %}>منخفضة</option>
                                <option value="medium" {% if tasks_priority_filter == 'medium' %}selected{% endif %}>متوسطة</option>
                                <option value="high" {% if tasks_priority_filter == 'high' %}selected{% endif %}>عالية</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="tasks_per_page" class="form-label">عدد النتائج</label>
                            <select class="form-select" id="tasks_per_page" name="tasks_per_page">
                                <option value="10" {% if tasks_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if tasks_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if tasks_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if tasks_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                        <div class="col-12 d-flex">
                            <button type="submit" class="btn btn-primary">تطبيق</button>
                            <a href="{{ url_for('employee.view', id=employee.id) }}" class="btn btn-secondary ms-2">إعادة ضبط</a>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card-body">
                {% if filtered_tasks.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>عنوان المهمة</th>
                                <th>المشروع</th>
                                <th>الحالة</th>
                                <th>الأولوية</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in filtered_tasks.items %}
                            <tr>
                                <td>{{ task.title }}</td>
                                <td>{{ task.project.name }}</td>
                                <td>
                                    {% if task.status == 'pending' %}
                                    <span class="badge bg-secondary">معلق</span>
                                    {% elif task.status == 'in_progress' %}
                                    <span class="badge bg-primary">قيد التنفيذ</span>
                                    {% elif task.status == 'completed' %}
                                    <span class="badge bg-success">مكتمل</span>
                                    {% elif task.status == 'cancelled' %}
                                    <span class="badge bg-danger">ملغي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if task.priority == 'low' %}
                                    <span class="badge bg-info">منخفضة</span>
                                    {% elif task.priority == 'medium' %}
                                    <span class="badge bg-warning text-dark">متوسطة</span>
                                    {% elif task.priority == 'high' %}
                                    <span class="badge bg-danger">عالية</span>
                                    {% endif %}
                                </td>
                                <td>{{ task.due_date.strftime('%Y-%m-%d') if task.due_date else 'غير محدد' }}</td>
                                <td>
                                    <a href="{{ url_for('project.tasks', id=task.project_id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>

                    <!-- Tasks Pagination -->
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            {% if filtered_tasks.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employee.view', id=employee.id, tasks_page=filtered_tasks.prev_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for page_num in filtered_tasks.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
                                {% if page_num %}
                                    {% if page_num == filtered_tasks.page %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('employee.view', id=employee.id, tasks_page=page_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}">{{ page_num }}</a>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if filtered_tasks.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('employee.view', id=employee.id, tasks_page=filtered_tasks.next_num, tasks_per_page=tasks_per_page, tasks_status=tasks_status_filter, tasks_priority=tasks_priority_filter, projects_page=projects.page, projects_per_page=projects_per_page, projects_status=projects_status_filter) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا يوجد مهام مسندة حاليًا أو لا توجد مشاريع مشتركة.</p>
                {% endif %}

                {% if not current_user.has_role('admin') and not current_user.has_role('manager') and current_user.id != employee.id %}
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    ملاحظة: يتم عرض المهام من المشاريع المشتركة بينك وبين هذا الموظف فقط.
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Penalties Section - Only visible to admin and manager -->
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">العقوبات</h5>
                <a href="{{ url_for('penalty.create') }}?user_id={{ employee.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة عقوبة
                </a>
            </div>
            <div class="card-body">
                {% if employee.penalties %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع العقوبة</th>
                                <th>السبب</th>
                                <th>تاريخ البدء</th>
                                <th>تاريخ الانتهاء</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for penalty in employee.penalties %}
                            <tr>
                                <td>{{ penalty.get_penalty_type_display() }}</td>
                                <td>{{ penalty.reason }}</td>
                                <td>{{ penalty.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ penalty.end_date.strftime('%Y-%m-%d') if penalty.end_date else 'غير محدد' }}</td>
                                <td>
                                    {% if penalty.is_active() %}
                                    <span class="badge bg-danger">سارية</span>
                                    {% else %}
                                    <span class="badge bg-secondary">منتهية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('penalty.view', id=penalty.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted mb-0">لا توجد عقوبات مسجلة لهذا الموظف.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- ID Documents Section - Only visible to admin and manager -->
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">الاثباتات الوطنية</h5>
                <a href="{{ url_for('employee.add_id_document', id=employee.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>إضافة وثيقة
                </a>
            </div>
            <div class="card-body">
                {% if employee.id_documents.count() > 0 %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>نوع الوثيقة</th>
                                <th>الرقم</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الانتهاء</th>
                                <th>بلد الإصدار</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in employee.id_documents %}
                            <tr>
                                <td>{{ doc.document_type }}</td>
                                <td>{{ doc.document_number }}</td>
                                <td>{{ doc.issue_date.strftime('%Y-%m-%d') if doc.issue_date else 'غير محدد' }}</td>
                                <td>{{ doc.expiry_date.strftime('%Y-%m-%d') if doc.expiry_date else 'غير محدد' }}</td>
                                <td>{{ doc.issuing_country }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('employee.view_id_document', id=doc.id) }}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('employee.edit_id_document', id=doc.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('employee.delete_id_document', id=doc.id) }}" class="btn btn-sm btn-danger">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">لا توجد وثائق هوية مسجلة.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Bank Account Section - Only visible to admin and manager -->
        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">حساب التحصيل المالي</h5>
            </div>
            <div class="card-body">
                {% if employee.bank_account %}
                <p>{{ employee.bank_account }}</p>
                {% else %}
                <p class="text-muted">لا يوجد حساب مصرفي مسجل.</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Active Leave Section -->
        {% if active_leave %}
        <div class="card shadow mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-umbrella-beach me-2"></i>إجازة حالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-2"><strong>تاريخ البداية:</strong> {{ active_leave.start_date.strftime('%Y-%m-%d') }}</p>
                        <p class="mb-2"><strong>تاريخ النهاية:</strong> {{ active_leave.end_date.strftime('%Y-%m-%d') }}</p>
                        <p class="mb-2"><strong>المدة:</strong> {{ active_leave.get_duration_days() }} يوم</p>
                    </div>
                    <div class="col-md-6">
                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <p class="mb-2"><strong>السبب:</strong> {{ active_leave.reason }}</p>
                        <p class="mb-0"><strong>تمت الموافقة بواسطة:</strong>
                            {% if active_leave.reviewed_by %}
                            {{ active_leave.reviewed_by.get_full_name() }}
                            {% else %}
                            غير محدد
                            {% endif %}
                        </p>
                        {% else %}
                        <p class="mb-0 text-muted"><i class="fas fa-info-circle me-1"></i>تفاصيل إضافية متاحة للمدراء والمسؤولين فقط</p>
                        {% endif %}
                    </div>
                </div>
                {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                <div class="mt-3">
                    <a href="{{ url_for('leave.view', id=active_leave.id) }}" class="btn btn-sm btn-info">
                        <i class="fas fa-eye me-1"></i>عرض تفاصيل الإجازة
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Honorary Ranks Section -->
        {% if employee.honorary_ranks %}
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">الرتب الشرفية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for rank in employee.honorary_ranks %}
                    <div class="col-md-6 mb-3">
                        <div class="card h-100">
                            <div class="card-header" style="background-color: {{ rank.color }}; color: {{ '#000' if rank.color == '#ffffff' else '#fff' }}">
                                <h6 class="mb-0">
                                    <i class="fas {{ rank.icon }} me-2"></i>{{ rank.name }}
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-0">{{ rank.description }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- CV/Resume Section -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">السيرة الذاتية</h5>
                {% if current_user.id == employee.id or current_user.has_role('admin') or current_user.has_role('manager') %}
                <a href="{{ url_for('employee.edit_cv', id=employee.id) }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-edit me-1"></i>تعديل السيرة الذاتية
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="cv-content">
                    {% if employee.cv %}
                    {{ employee.cv|nl2br|safe }}
                    {% else %}
                    <p class="text-muted">لا توجد سيرة ذاتية متاحة.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">النشاط الأخير</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% if recent_projects %}
                        {% for project in recent_projects %}
                        <div class="timeline-item">
                            <div class="timeline-date">{{ project.created_at.strftime('%Y-%m-%d') }}</div>
                            <div class="timeline-content">
                                <div class="timeline-title">تم الانضمام إلى مشروع</div>
                                <div class="timeline-text">تم الانضمام إلى مشروع "{{ project.name }}"</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        لا يوجد نشاط حديث لهذا الموظف.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
<style>
    .timeline {
        position: relative;
        padding: 0;
        list-style: none;
    }

    .timeline-item {
        position: relative;
        padding-left: 40px;
        margin-bottom: 20px;
    }

    .timeline-item:before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #007bff;
    }

    .timeline-item:after {
        content: '';
        position: absolute;
        left: 5px;
        top: 12px;
        bottom: -20px;
        width: 2px;
        background-color: #e9ecef;
    }

    .timeline-item:last-child:after {
        display: none;
    }

    .timeline-date {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .timeline-title {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .timeline-text {
        color: #495057;
    }
</style>
{% endblock %}
