"""
مسارات المعاملات المالية الجديدة
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.accounting import Account, AccountingSettings
from app.models.finance import Transaction, TransactionItem
from app.models.project import Project
from app.models.task import Task
from app.models.user import User
from datetime import datetime
import json
import os
from werkzeug.utils import secure_filename

transactions_bp = Blueprint('transactions', __name__, url_prefix='/transactions')

# ==================== قائمة المعاملات ====================

@transactions_bp.route('/')
@login_required
def index():
    """صفحة قائمة المعاملات"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # فلترة
    status_filter = request.args.get('status')
    search_filter = request.args.get('search')
    
    query = Transaction.query
    
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    if search_filter:
        query = query.filter(
            db.or_(
                Transaction.name.contains(search_filter),
                Transaction.transaction_number.contains(search_filter),
                Transaction.description.contains(search_filter)
            )
        )
    
    transactions = query.order_by(Transaction.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات
    stats = {
        'total': Transaction.query.count(),
        'draft': Transaction.query.filter_by(status='draft').count(),
        'pending': Transaction.query.filter_by(status='pending').count(),
        'paid': Transaction.query.filter_by(status='paid').count(),
        'cancelled': Transaction.query.filter_by(status='cancelled').count(),
    }
    
    return render_template('transactions/index.html',
                          title='إدارة المعاملات المالية',
                          transactions=transactions,
                          stats=stats,
                          status_filter=status_filter,
                          search_filter=search_filter)

# ==================== إنشاء معاملة ====================

@transactions_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """إنشاء معاملة جديدة"""
    if request.method == 'POST':
        try:
            # البيانات الأساسية
            transaction_number = request.form.get('transaction_number', '').strip()
            name = request.form.get('name', '').strip()
            description = request.form.get('description', '').strip()
            transaction_date = request.form.get('transaction_date')
            status = request.form.get('status', 'draft')
            notes = request.form.get('notes', '').strip()
            
            # الروابط
            links = []
            link_urls = request.form.getlist('link_url')
            link_titles = request.form.getlist('link_title')
            for url, title in zip(link_urls, link_titles):
                if url.strip():
                    links.append({'url': url.strip(), 'title': title.strip() or url.strip()})
            
            # التحقق من البيانات
            if not transaction_number or not name:
                flash('رقم المعاملة واسم المعاملة مطلوبان', 'danger')
                return redirect(url_for('transactions.create'))
            
            # التحقق من عدم تكرار رقم المعاملة
            existing_transaction = Transaction.query.filter_by(transaction_number=transaction_number).first()
            if existing_transaction:
                flash('رقم المعاملة موجود مسبقاً', 'danger')
                return redirect(url_for('transactions.create'))
            
            # إنشاء المعاملة
            transaction = Transaction(
                transaction_number=transaction_number,
                name=name,
                description=description,
                transaction_date=datetime.strptime(transaction_date, '%Y-%m-%dT%H:%M') if transaction_date else datetime.utcnow(),
                status=status,
                notes=notes,
                created_by_id=current_user.id
            )
            
            # تعيين الروابط
            transaction.set_links(links)
            
            db.session.add(transaction)
            db.session.flush()  # للحصول على ID
            
            # معالجة المرفقات
            attachments = []
            uploaded_files = request.files.getlist('attachments')
            for file in uploaded_files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    # حفظ الملف
                    upload_folder = os.path.join('app', 'static', 'uploads', 'transactions')
                    os.makedirs(upload_folder, exist_ok=True)
                    file_path = os.path.join(upload_folder, f"{transaction.id}_{filename}")
                    file.save(file_path)
                    attachments.append({
                        'filename': filename,
                        'path': f"uploads/transactions/{transaction.id}_{filename}"
                    })
            
            transaction.set_attachments(attachments)
            
            # معالجة بنود المعاملة
            account_ids = request.form.getlist('item_account_id')
            operations = request.form.getlist('item_operation')
            amounts = request.form.getlist('item_amount')
            item_dates = request.form.getlist('item_date')
            item_descriptions = request.form.getlist('item_description')
            
            for i, account_id in enumerate(account_ids):
                if account_id and i < len(operations) and i < len(amounts):
                    try:
                        amount = float(amounts[i])
                        if amount > 0:
                            item = TransactionItem(
                                transaction_id=transaction.id,
                                account_id=int(account_id),
                                operation=operations[i],
                                amount=amount,
                                item_date=datetime.strptime(item_dates[i], '%Y-%m-%dT%H:%M') if i < len(item_dates) and item_dates[i] else datetime.utcnow(),
                                description=item_descriptions[i] if i < len(item_descriptions) else ''
                            )
                            db.session.add(item)
                    except (ValueError, IndexError):
                        continue
            
            # تحديث الحسابات حسب الحالة
            if status in ['pending', 'paid']:
                transaction.update_status(status)
            
            db.session.commit()
            
            flash('تم إنشاء المعاملة بنجاح', 'success')
            return redirect(url_for('transactions.view', transaction_id=transaction.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء المعاملة: {str(e)}', 'danger')
    
    # الحصول على البيانات للنموذج
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    # إنشاء رقم معاملة تلقائي
    last_transaction = Transaction.query.order_by(Transaction.id.desc()).first()
    next_number = (last_transaction.id + 1) if last_transaction else 1
    suggested_number = f"TXN-{next_number:06d}"
    
    return render_template('transactions/create.html',
                          title='إنشاء معاملة جديدة',
                          accounts=accounts,
                          suggested_number=suggested_number)

# ==================== عرض معاملة ====================

@transactions_bp.route('/<int:transaction_id>')
@login_required
def view(transaction_id):
    """عرض تفاصيل معاملة"""
    transaction = Transaction.query.get_or_404(transaction_id)
    
    return render_template('transactions/view.html',
                          title=f'المعاملة {transaction.transaction_number}',
                          transaction=transaction)

# ==================== تعديل معاملة ====================

@transactions_bp.route('/<int:transaction_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(transaction_id):
    """تعديل معاملة"""
    transaction = Transaction.query.get_or_404(transaction_id)
    
    if request.method == 'POST':
        try:
            old_status = transaction.status
            
            # البيانات الأساسية
            transaction.transaction_number = request.form.get('transaction_number', '').strip()
            transaction.name = request.form.get('name', '').strip()
            transaction.description = request.form.get('description', '').strip()
            transaction_date = request.form.get('transaction_date')
            transaction.transaction_date = datetime.strptime(transaction_date, '%Y-%m-%dT%H:%M') if transaction_date else datetime.utcnow()
            new_status = request.form.get('status', 'draft')
            transaction.notes = request.form.get('notes', '').strip()
            
            # الروابط
            links = []
            link_urls = request.form.getlist('link_url')
            link_titles = request.form.getlist('link_title')
            for url, title in zip(link_urls, link_titles):
                if url.strip():
                    links.append({'url': url.strip(), 'title': title.strip() or url.strip()})
            transaction.set_links(links)
            
            # التحقق من البيانات
            if not transaction.transaction_number or not transaction.name:
                flash('رقم المعاملة واسم المعاملة مطلوبان', 'danger')
                return redirect(url_for('transactions.edit', transaction_id=transaction_id))
            
            # التحقق من عدم تكرار رقم المعاملة
            existing_transaction = Transaction.query.filter_by(transaction_number=transaction.transaction_number).first()
            if existing_transaction and existing_transaction.id != transaction.id:
                flash('رقم المعاملة موجود مسبقاً', 'danger')
                return redirect(url_for('transactions.edit', transaction_id=transaction_id))
            
            # حذف البنود القديمة
            TransactionItem.query.filter_by(transaction_id=transaction.id).delete()
            
            # إضافة البنود الجديدة
            account_ids = request.form.getlist('item_account_id')
            operations = request.form.getlist('item_operation')
            amounts = request.form.getlist('item_amount')
            item_dates = request.form.getlist('item_date')
            item_descriptions = request.form.getlist('item_description')
            
            for i, account_id in enumerate(account_ids):
                if account_id and i < len(operations) and i < len(amounts):
                    try:
                        amount = float(amounts[i])
                        if amount > 0:
                            item = TransactionItem(
                                transaction_id=transaction.id,
                                account_id=int(account_id),
                                operation=operations[i],
                                amount=amount,
                                item_date=datetime.strptime(item_dates[i], '%Y-%m-%dT%H:%M') if i < len(item_dates) and item_dates[i] else datetime.utcnow(),
                                description=item_descriptions[i] if i < len(item_descriptions) else ''
                            )
                            db.session.add(item)
                    except (ValueError, IndexError):
                        continue
            
            transaction.updated_at = datetime.utcnow()
            
            # تحديث الحالة إذا تغيرت
            if old_status != new_status:
                transaction.update_status(new_status)
            
            db.session.commit()
            
            flash('تم تحديث المعاملة بنجاح', 'success')
            return redirect(url_for('transactions.view', transaction_id=transaction_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المعاملة: {str(e)}', 'danger')
    
    # الحصول على البيانات للنموذج
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    return render_template('transactions/edit.html',
                          title=f'تعديل المعاملة {transaction.transaction_number}',
                          transaction=transaction,
                          accounts=accounts)

# ==================== تحديث حالة معاملة ====================

@transactions_bp.route('/<int:transaction_id>/update_status', methods=['POST'])
@login_required
def update_status(transaction_id):
    """تحديث حالة معاملة"""
    transaction = Transaction.query.get_or_404(transaction_id)
    
    try:
        new_status = request.form.get('status')
        
        if new_status not in ['draft', 'pending', 'paid', 'cancelled']:
            return jsonify({'success': False, 'message': 'حالة غير صحيحة'}), 400
        
        transaction.update_status(new_status)
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة المعاملة بنجاح',
            'status': transaction.status_display,
            'color': transaction.status_color
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

# ==================== حذف معاملة ====================

@transactions_bp.route('/<int:transaction_id>/delete', methods=['POST'])
@login_required
def delete(transaction_id):
    """حذف معاملة"""
    transaction = Transaction.query.get_or_404(transaction_id)
    
    try:
        # التراجع عن تأثير المعاملة على الحسابات
        if transaction.status in ['pending', 'paid']:
            transaction.update_status('draft')
        
        db.session.delete(transaction)
        db.session.commit()
        
        flash('تم حذف المعاملة بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المعاملة: {str(e)}', 'danger')
    
    return redirect(url_for('transactions.index'))
