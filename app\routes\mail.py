import os
from datetime import datetime
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, send_from_directory, jsonify
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from sqlalchemy import or_, and_

from app import db
from app.models.mail import Mail, MailAttachment, mail_recipients
from app.models.user import User

mail_bp = Blueprint('mail', __name__, url_prefix='/mail')

@mail_bp.route('/get_unread_count')
@login_required
def get_unread_count():
    """Get the number of unread mail messages for the current user"""
    # Count mails that have not been read by current user
    unread_count = db.session.query(mail_recipients).filter(
        mail_recipients.c.user_id == current_user.id,
        mail_recipients.c.read_at.is_(None)
    ).count()

    return jsonify({'count': unread_count})

@mail_bp.route('/')
@login_required
def index():
    """Página principal del correo (bandeja de entrada)"""
    return redirect(url_for('mail.inbox'))

@mail_bp.route('/inbox')
@login_required
def inbox():
    """Bandeja de entrada"""
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    read_status = request.args.get('read_status', '')
    has_attachments = request.args.get('has_attachments', '')

    # Build the base query
    query = Mail.query.join(Mail.recipients).filter(
        User.id == current_user.id,
        Mail.is_draft == False
    )

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Mail.subject.like(search_term),
                Mail.body.like(search_term),
                User.first_name.like(search_term),
                User.last_name.like(search_term)
            )
        )

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Mail.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Mail.created_at <= to_date)
        except ValueError:
            pass

    # Apply read status filter if provided
    if read_status:
        from app.models.mail import mail_read_status
        if read_status == 'read':
            # Find mails that have been read by current user
            query = query.join(mail_read_status).filter(
                mail_read_status.c.user_id == current_user.id
            )
        elif read_status == 'unread':
            # Find mails that have not been read by current user
            read_mail_ids = db.session.query(mail_read_status.c.mail_id).filter(
                mail_read_status.c.user_id == current_user.id
            ).subquery()
            query = query.filter(~Mail.id.in_(read_mail_ids))

    # Apply attachments filter if provided
    if has_attachments:
        if has_attachments == 'yes':
            query = query.join(Mail.attachments).group_by(Mail.id)
        elif has_attachments == 'no':
            # Find mails without attachments
            mail_with_attachments = db.session.query(MailAttachment.mail_id).distinct().subquery()
            query = query.filter(~Mail.id.in_(mail_with_attachments))

    # Order by created_at (newest first)
    query = query.order_by(Mail.created_at.desc())

    # Paginate the results
    mails = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('mail/inbox.html',
                          title='البريد الوارد',
                          mails=mails,
                          folder='inbox',
                          search_query=search_query,
                          date_from=date_from,
                          date_to=date_to,
                          read_status=read_status,
                          has_attachments=has_attachments,
                          current_per_page=per_page)

@mail_bp.route('/sent')
@login_required
def sent():
    """Correos enviados"""
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    has_attachments = request.args.get('has_attachments', '')

    # Build the base query
    query = Mail.query.filter_by(
        sender_id=current_user.id,
        is_draft=False
    )

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Mail.subject.like(search_term),
                Mail.body.like(search_term)
            )
        )

        # Search by recipient name (more complex)
        if search_term:
            recipient_ids = db.session.query(User.id).filter(
                or_(
                    User.first_name.like(search_term),
                    User.last_name.like(search_term)
                )
            ).all()
            recipient_ids = [r[0] for r in recipient_ids]

            if recipient_ids:
                from app.models.mail import mail_recipients
                recipient_mail_ids = db.session.query(mail_recipients.c.mail_id).filter(
                    mail_recipients.c.user_id.in_(recipient_ids)
                ).all()
                recipient_mail_ids = [m[0] for m in recipient_mail_ids]

                # Add to the existing query with OR condition
                query = query.filter(
                    or_(
                        Mail.id.in_(recipient_mail_ids),
                        Mail.subject.like(search_term),
                        Mail.body.like(search_term)
                    )
                )

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Mail.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Mail.created_at <= to_date)
        except ValueError:
            pass

    # Apply attachments filter if provided
    if has_attachments:
        if has_attachments == 'yes':
            query = query.join(Mail.attachments).group_by(Mail.id)
        elif has_attachments == 'no':
            # Find mails without attachments
            mail_with_attachments = db.session.query(MailAttachment.mail_id).distinct().subquery()
            query = query.filter(~Mail.id.in_(mail_with_attachments))

    # Order by created_at (newest first)
    query = query.order_by(Mail.created_at.desc())

    # Paginate the results
    mails = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('mail/inbox.html',
                          title='البريد المرسل',
                          mails=mails,
                          folder='sent',
                          search_query=search_query,
                          date_from=date_from,
                          date_to=date_to,
                          has_attachments=has_attachments,
                          current_per_page=per_page)

@mail_bp.route('/drafts')
@login_required
def drafts():
    """Borradores"""
    # Get query parameters for pagination and search
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 25, type=int)
    search_query = request.args.get('search', '')
    date_from = request.args.get('date_from', '')
    date_to = request.args.get('date_to', '')
    has_attachments = request.args.get('has_attachments', '')

    # Build the base query
    query = Mail.query.filter_by(
        sender_id=current_user.id,
        is_draft=True
    )

    # Apply search filter if provided
    if search_query:
        search_term = f"%{search_query}%"
        query = query.filter(
            or_(
                Mail.subject.like(search_term),
                Mail.body.like(search_term)
            )
        )

    # Apply date range filters if provided
    if date_from:
        try:
            from_date = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Mail.created_at >= from_date)
        except ValueError:
            pass

    if date_to:
        try:
            to_date = datetime.strptime(date_to, '%Y-%m-%d')
            to_date = to_date.replace(hour=23, minute=59, second=59)
            query = query.filter(Mail.created_at <= to_date)
        except ValueError:
            pass

    # Apply attachments filter if provided
    if has_attachments:
        if has_attachments == 'yes':
            query = query.join(Mail.attachments).group_by(Mail.id)
        elif has_attachments == 'no':
            # Find mails without attachments
            mail_with_attachments = db.session.query(MailAttachment.mail_id).distinct().subquery()
            query = query.filter(~Mail.id.in_(mail_with_attachments))

    # Order by created_at (newest first)
    query = query.order_by(Mail.created_at.desc())

    # Paginate the results
    mails = query.paginate(page=page, per_page=per_page, error_out=False)

    return render_template('mail/inbox.html',
                          title='المسودات',
                          mails=mails,
                          folder='drafts',
                          search_query=search_query,
                          date_from=date_from,
                          date_to=date_to,
                          has_attachments=has_attachments,
                          current_per_page=per_page)

@mail_bp.route('/compose', methods=['GET', 'POST'])
@login_required
def compose():
    """Redactar un nuevo correo"""
    if request.method == 'POST':
        subject = request.form.get('subject', '')
        body = request.form.get('body', '')
        recipient_ids = request.form.getlist('recipients')
        is_draft = 'save_draft' in request.form

        # Validar que haya al menos un destinatario si no es un borrador
        if not is_draft and not recipient_ids:
            flash('يجب تحديد مستلم واحد على الأقل', 'danger')
            return render_template('mail/compose.html',
                                  title='رسالة جديدة',
                                  subject=subject,
                                  body=body)

        # Crear el nuevo correo
        mail = Mail(
            subject=subject,
            body=body,
            sender_id=current_user.id,
            is_draft=is_draft
        )

        # Añadir destinatarios
        if recipient_ids:
            recipients = User.query.filter(User.id.in_(recipient_ids)).all()
            mail.recipients = recipients

        db.session.add(mail)
        db.session.commit()

        # Procesar archivos adjuntos
        if 'attachments' in request.files:
            files = request.files.getlist('attachments')
            for file in files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    # Crear directorio para archivos adjuntos si no existe
                    attachments_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'mail_attachments', str(mail.id))
                    os.makedirs(attachments_dir, exist_ok=True)

                    file_path = os.path.join(attachments_dir, filename)
                    file.save(file_path)

                    # Guardar información del archivo en la base de datos
                    attachment = MailAttachment(
                        filename=filename,
                        file_path=os.path.join('mail_attachments', str(mail.id), filename),
                        file_size=os.path.getsize(file_path),
                        mime_type=file.content_type or 'application/octet-stream',
                        mail_id=mail.id
                    )
                    db.session.add(attachment)

            db.session.commit()

        if is_draft:
            flash('تم حفظ المسودة بنجاح', 'success')
            return redirect(url_for('mail.drafts'))
        else:
            flash('تم إرسال البريد بنجاح', 'success')
            return redirect(url_for('mail.sent'))

    # Obtener todos los usuarios para el selector de destinatarios (filter inactive users for non-admin/manager)
    users_query = User.query.filter(User.id != current_user.id)

    # Filter inactive users for non-admin/manager
    if not (current_user.has_role('admin') or current_user.has_role('manager')):
        users_query = users_query.filter(User.is_active == True)

    users = users_query.order_by(User.first_name).all()

    # Verificar si es una respuesta a otro correo
    reply_to_id = request.args.get('reply_to', None)
    forward_id = request.args.get('forward', None)

    subject = ''
    body = ''
    recipients = []

    if reply_to_id:
        original_mail = Mail.query.get_or_404(reply_to_id)
        subject = f'رد: {original_mail.subject}'
        body = f'<br><br><hr><p>في {original_mail.created_at.strftime("%Y-%m-%d %H:%M")}, كتب {original_mail.sender.first_name} {original_mail.sender.last_name}:</p><blockquote>{original_mail.body}</blockquote>'
        recipients = [original_mail.sender]

    elif forward_id:
        original_mail = Mail.query.get_or_404(forward_id)
        subject = f'إعادة توجيه: {original_mail.subject}'
        body = f'<br><br><hr><p>رسالة معاد توجيهها:</p><p>من: {original_mail.sender.first_name} {original_mail.sender.last_name}<br>تاريخ: {original_mail.created_at.strftime("%Y-%m-%d %H:%M")}<br>إلى: {original_mail.get_all_recipients_names()}<br>الموضوع: {original_mail.subject}</p><br>{original_mail.body}'

    return render_template('mail/compose.html',
                          title='رسالة جديدة',
                          users=users,
                          subject=subject,
                          body=body,
                          recipients=recipients)

@mail_bp.route('/view/<int:id>')
@login_required
def view(id):
    """Ver un correo específico"""
    mail = Mail.query.get_or_404(id)

    # Verificar que el usuario actual tenga permiso para ver este correo
    if mail.sender_id != current_user.id and current_user not in mail.recipients:
        flash('ليس لديك صلاحية لعرض هذا البريد', 'danger')
        return redirect(url_for('mail.inbox'))

    # Marcar como leído si el usuario actual es destinatario
    if current_user in mail.recipients:
        mail.mark_as_read(current_user.id)

    return render_template('mail/view.html',
                          title=mail.subject,
                          mail=mail)

@mail_bp.route('/confirm-delete/<int:id>')
@login_required
def confirm_delete(id):
    """Página de confirmación para eliminar un correo"""
    mail = Mail.query.get_or_404(id)

    # Verificar que el usuario actual tenga permiso para eliminar este correo
    if mail.sender_id != current_user.id and current_user not in mail.recipients:
        flash('ليس لديك صلاحية لحذف هذا البريد', 'danger')
        return redirect(url_for('mail.inbox'))

    return render_template('mail/confirm_delete.html',
                          title='تأكيد حذف البريد',
                          mail=mail)

@mail_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
def delete(id):
    """Eliminar un correo"""
    mail = Mail.query.get_or_404(id)

    # Verificar que el usuario actual tenga permiso para eliminar este correo
    if mail.sender_id != current_user.id and current_user not in mail.recipients:
        flash('ليس لديك صلاحية لحذف هذا البريد', 'danger')
        return redirect(url_for('mail.inbox'))

    # Eliminar archivos adjuntos
    for attachment in mail.attachments:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], attachment.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)

    db.session.delete(mail)
    db.session.commit()

    flash('تم حذف البريد بنجاح', 'success')
    return redirect(url_for('mail.inbox'))

@mail_bp.route('/download/<int:id>')
@login_required
def download_attachment(id):
    """Descargar un archivo adjunto"""
    attachment = MailAttachment.query.get_or_404(id)
    mail = Mail.query.get_or_404(attachment.mail_id)

    # Verificar que el usuario actual tenga permiso para descargar este archivo
    if mail.sender_id != current_user.id and current_user not in mail.recipients:
        flash('ليس لديك صلاحية لتنزيل هذا الملف', 'danger')
        return redirect(url_for('mail.inbox'))

    directory = os.path.join(current_app.config['UPLOAD_FOLDER'], os.path.dirname(attachment.file_path))
    filename = os.path.basename(attachment.file_path)

    return send_from_directory(directory, filename, as_attachment=True)
