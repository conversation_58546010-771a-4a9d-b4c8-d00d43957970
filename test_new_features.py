#!/usr/bin/env python3
"""
Test script for new features
"""

from app import create_app, db
from app.models.timeline import ProjectTimeline
from app.models.project import Project

def test_timeline_routes():
    """Test timeline management routes"""
    print("🧪 Testing timeline management routes...")
    
    try:
        # Get first timeline
        timeline = ProjectTimeline.query.first()
        if not timeline:
            print("⚠️ No timelines found - creating test timeline")
            project = Project.query.first()
            if not project:
                print("❌ No projects found")
                return False
            
            from app.models.user import User
            user = User.query.first()
            
            timeline = ProjectTimeline(
                name="Test Timeline",
                description="Test timeline for testing",
                project_id=project.id,
                created_by_id=user.id
            )
            db.session.add(timeline)
            db.session.commit()
            print(f"✅ Created test timeline: {timeline.name}")
        
        print(f"✅ Found timeline: {timeline.name}")
        print(f"✅ Timeline ID: {timeline.id}")
        print(f"✅ Project: {timeline.project.name}")
        print(f"✅ Steps count: {timeline.steps.count()}")
        print(f"✅ Created by: {timeline.created_by.get_full_name()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Timeline routes test failed: {e}")
        return False

def test_timeline_model():
    """Test timeline model functionality"""
    print("\n🧪 Testing timeline model functionality...")
    
    try:
        timeline = ProjectTimeline.query.first()
        if not timeline:
            print("❌ No timeline found")
            return False
        
        # Test model properties
        print(f"✅ Timeline name: {timeline.name}")
        print(f"✅ Timeline description: {timeline.description or 'No description'}")
        print(f"✅ Timeline project: {timeline.project.name}")
        print(f"✅ Timeline steps: {timeline.steps.count()}")
        
        # Test relationships
        if timeline.steps.count() > 0:
            step = timeline.steps.first()
            print(f"✅ First step: {step.title}")
            print(f"✅ Step status: {step.get_status_display()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Timeline model test failed: {e}")
        return False

def test_project_task_data():
    """Test project and task data for notifications"""
    print("\n🧪 Testing project and task data for notifications...")
    
    try:
        from app.models.task import Task
        
        # Test projects
        projects = Project.query.all()
        print(f"✅ Found {len(projects)} projects")
        
        if projects:
            project = projects[0]
            print(f"✅ Sample project: {project.name}")
            print(f"✅ Project description: {project.description[:50] if project.description else 'No description'}...")
        
        # Test tasks
        tasks = Task.query.all()
        print(f"✅ Found {len(tasks)} tasks")
        
        if tasks:
            task = tasks[0]
            print(f"✅ Sample task: {task.title}")
            print(f"✅ Task project: {task.project.name if task.project else 'No project'}")
            print(f"✅ Task assigned to: {task.assigned_to.get_full_name() if task.assigned_to else 'Unassigned'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Project/Task data test failed: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Testing new features...\n")
    
    # Create Flask app context
    app = create_app()
    
    with app.app_context():
        test1 = test_timeline_routes()
        test2 = test_timeline_model()
        test3 = test_project_task_data()
        
        print("\n" + "="*50)
        
        if test1 and test2 and test3:
            print("🎉 ALL NEW FEATURES TESTS PASSED!")
            print("\n✅ New features ready:")
            print("   - Timeline edit/delete functionality")
            print("   - Enhanced notification search")
            print("   - Project and task search dropdowns")
            print("   - Timeline management interface")
            return True
        else:
            print("💥 Some tests failed! Please check the issues above.")
            return False

if __name__ == '__main__':
    import sys
    success = main()
    sys.exit(0 if success else 1)
