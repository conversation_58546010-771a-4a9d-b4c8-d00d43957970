"""
مسارات نظام الرواتب
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.accounting import Account, AccountingSettings
from app.models.finance import PayrollEntry
from app.models.project import Project
from app.models.task import Task
from app.models.user import User
from datetime import datetime
import json
import os
from werkzeug.utils import secure_filename

payroll_bp = Blueprint('payroll', __name__, url_prefix='/payroll')

# ==================== قائمة الرواتب ====================

@payroll_bp.route('/')
@login_required
def index():
    """صفحة قائمة الرواتب"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # فلترة
    status_filter = request.args.get('status')
    employee_filter = request.args.get('employee')
    
    query = PayrollEntry.query
    
    if status_filter:
        query = query.filter_by(status=status_filter)
    
    if employee_filter:
        query = query.filter(PayrollEntry.employee_name.contains(employee_filter))
    
    payrolls = query.order_by(PayrollEntry.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # إحصائيات
    stats = {
        'total': PayrollEntry.query.count(),
        'draft': PayrollEntry.query.filter_by(status='draft').count(),
        'pending': PayrollEntry.query.filter_by(status='pending').count(),
        'paid': PayrollEntry.query.filter_by(status='paid').count(),
        'cancelled': PayrollEntry.query.filter_by(status='cancelled').count(),
    }
    
    return render_template('payroll/index.html',
                          title='إدارة الرواتب',
                          payrolls=payrolls,
                          stats=stats,
                          status_filter=status_filter,
                          employee_filter=employee_filter)

# ==================== إنشاء راتب ====================

@payroll_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """إنشاء راتب جديد"""
    if request.method == 'POST':
        try:
            # البيانات الأساسية
            employee_name = request.form.get('employee_name', '').strip()
            employee_user_id = request.form.get('employee_user_id') or None
            amount = float(request.form.get('amount', 0))
            status = request.form.get('status', 'draft')
            transfer_date = request.form.get('transfer_date')
            supervisor_id = request.form.get('supervisor_id') or None
            notes = request.form.get('notes', '').strip()
            
            # الحسابات
            debit_account_id = request.form.get('debit_account_id') or None
            pending_account_id = request.form.get('pending_account_id') or None
            paid_account_id = request.form.get('paid_account_id') or None
            
            # المشاريع والمهام
            related_projects = request.form.getlist('related_projects')
            related_tasks = request.form.getlist('related_tasks')
            
            # الروابط
            links = []
            link_urls = request.form.getlist('link_url')
            link_titles = request.form.getlist('link_title')
            for url, title in zip(link_urls, link_titles):
                if url.strip():
                    links.append({'url': url.strip(), 'title': title.strip() or url.strip()})
            
            # التحقق من البيانات
            if not employee_name or amount <= 0:
                flash('اسم الموظف ومبلغ الراتب مطلوبان', 'danger')
                return redirect(url_for('payroll.create'))
            
            # إنشاء الراتب
            payroll = PayrollEntry(
                employee_name=employee_name,
                employee_user_id=int(employee_user_id) if employee_user_id else None,
                amount=amount,
                status=status,
                transfer_date=datetime.strptime(transfer_date, '%Y-%m-%dT%H:%M') if transfer_date else None,
                supervisor_id=int(supervisor_id) if supervisor_id else None,
                debit_account_id=int(debit_account_id) if debit_account_id else None,
                pending_account_id=int(pending_account_id) if pending_account_id else None,
                paid_account_id=int(paid_account_id) if paid_account_id else None,
                notes=notes,
                created_by_id=current_user.id
            )
            
            # تعيين المشاريع والمهام
            payroll.set_related_projects([int(p) for p in related_projects if p])
            payroll.set_related_tasks([int(t) for t in related_tasks if t])
            payroll.set_links(links)
            
            db.session.add(payroll)
            db.session.flush()  # للحصول على ID
            
            # معالجة المرفقات
            attachments = []
            uploaded_files = request.files.getlist('attachments')
            for file in uploaded_files:
                if file and file.filename:
                    filename = secure_filename(file.filename)
                    # حفظ الملف (يجب إنشاء مجلد المرفقات)
                    upload_folder = os.path.join('app', 'static', 'uploads', 'payroll')
                    os.makedirs(upload_folder, exist_ok=True)
                    file_path = os.path.join(upload_folder, f"{payroll.id}_{filename}")
                    file.save(file_path)
                    attachments.append({
                        'filename': filename,
                        'path': f"uploads/payroll/{payroll.id}_{filename}"
                    })
            
            payroll.set_attachments(attachments)
            
            # تحديث الحسابات حسب الحالة
            if status in ['pending', 'paid']:
                payroll.update_status(status)
            
            db.session.commit()
            
            flash('تم إنشاء الراتب بنجاح', 'success')
            return redirect(url_for('payroll.view', payroll_id=payroll.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء الراتب: {str(e)}', 'danger')
    
    # الحصول على البيانات للنموذج
    settings = AccountingSettings.get_settings()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    users = User.query.filter_by(is_active=True).order_by(User.first_name, User.last_name).all()
    projects = Project.query.filter_by(is_active=True).order_by(Project.name).all()
    tasks = Task.query.order_by(Task.title).all()
    
    return render_template('payroll/create.html',
                          title='إنشاء راتب جديد',
                          settings=settings,
                          accounts=accounts,
                          users=users,
                          projects=projects,
                          tasks=tasks)

# ==================== عرض راتب ====================

@payroll_bp.route('/<int:payroll_id>')
@login_required
def view(payroll_id):
    """عرض تفاصيل راتب"""
    payroll = PayrollEntry.query.get_or_404(payroll_id)
    
    return render_template('payroll/view.html',
                          title=f'راتب {payroll.employee_name}',
                          payroll=payroll)

# ==================== تعديل راتب ====================

@payroll_bp.route('/<int:payroll_id>/edit', methods=['GET', 'POST'])
@login_required
def edit(payroll_id):
    """تعديل راتب"""
    payroll = PayrollEntry.query.get_or_404(payroll_id)
    
    if request.method == 'POST':
        try:
            old_status = payroll.status
            
            # البيانات الأساسية
            payroll.employee_name = request.form.get('employee_name', '').strip()
            payroll.employee_user_id = int(request.form.get('employee_user_id')) if request.form.get('employee_user_id') else None
            payroll.amount = float(request.form.get('amount', 0))
            new_status = request.form.get('status', 'draft')
            transfer_date = request.form.get('transfer_date')
            payroll.transfer_date = datetime.strptime(transfer_date, '%Y-%m-%dT%H:%M') if transfer_date else None
            payroll.supervisor_id = int(request.form.get('supervisor_id')) if request.form.get('supervisor_id') else None
            payroll.notes = request.form.get('notes', '').strip()
            
            # الحسابات
            payroll.debit_account_id = int(request.form.get('debit_account_id')) if request.form.get('debit_account_id') else None
            payroll.pending_account_id = int(request.form.get('pending_account_id')) if request.form.get('pending_account_id') else None
            payroll.paid_account_id = int(request.form.get('paid_account_id')) if request.form.get('paid_account_id') else None
            
            # المشاريع والمهام
            related_projects = request.form.getlist('related_projects')
            related_tasks = request.form.getlist('related_tasks')
            payroll.set_related_projects([int(p) for p in related_projects if p])
            payroll.set_related_tasks([int(t) for t in related_tasks if t])
            
            # الروابط
            links = []
            link_urls = request.form.getlist('link_url')
            link_titles = request.form.getlist('link_title')
            for url, title in zip(link_urls, link_titles):
                if url.strip():
                    links.append({'url': url.strip(), 'title': title.strip() or url.strip()})
            payroll.set_links(links)
            
            # التحقق من البيانات
            if not payroll.employee_name or payroll.amount <= 0:
                flash('اسم الموظف ومبلغ الراتب مطلوبان', 'danger')
                return redirect(url_for('payroll.edit', payroll_id=payroll_id))
            
            payroll.updated_at = datetime.utcnow()
            
            # تحديث الحالة إذا تغيرت
            if old_status != new_status:
                payroll.update_status(new_status)
            
            db.session.commit()
            
            flash('تم تحديث الراتب بنجاح', 'success')
            return redirect(url_for('payroll.view', payroll_id=payroll_id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الراتب: {str(e)}', 'danger')
    
    # الحصول على البيانات للنموذج
    settings = AccountingSettings.get_settings()
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    users = User.query.filter_by(is_active=True).order_by(User.first_name, User.last_name).all()
    projects = Project.query.filter_by(is_active=True).order_by(Project.name).all()
    tasks = Task.query.order_by(Task.title).all()
    
    return render_template('payroll/edit.html',
                          title=f'تعديل راتب {payroll.employee_name}',
                          payroll=payroll,
                          settings=settings,
                          accounts=accounts,
                          users=users,
                          projects=projects,
                          tasks=tasks)

# ==================== تحديث حالة راتب ====================

@payroll_bp.route('/<int:payroll_id>/update_status', methods=['POST'])
@login_required
def update_status(payroll_id):
    """تحديث حالة راتب"""
    payroll = PayrollEntry.query.get_or_404(payroll_id)
    
    try:
        new_status = request.form.get('status')
        
        if new_status not in ['draft', 'pending', 'paid', 'cancelled']:
            return jsonify({'success': False, 'message': 'حالة غير صحيحة'}), 400
        
        payroll.update_status(new_status)
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث حالة الراتب بنجاح',
            'status': payroll.status_display,
            'color': payroll.status_color
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'حدث خطأ: {str(e)}'}), 500

# ==================== حذف راتب ====================

@payroll_bp.route('/<int:payroll_id>/delete', methods=['POST'])
@login_required
def delete(payroll_id):
    """حذف راتب"""
    payroll = PayrollEntry.query.get_or_404(payroll_id)
    
    try:
        # التراجع عن تأثير الراتب على الحسابات
        if payroll.status in ['pending', 'paid']:
            payroll.update_status('draft')
        
        db.session.delete(payroll)
        db.session.commit()
        
        flash('تم حذف الراتب بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الراتب: {str(e)}', 'danger')
    
    return redirect(url_for('payroll.index'))

# ==================== API ====================

@payroll_bp.route('/api/employees')
@login_required
def api_employees():
    """API للحصول على قائمة الموظفين"""
    search = request.args.get('search', '').strip()
    
    users = User.query.filter_by(is_active=True)
    
    if search:
        users = users.filter(
            db.or_(
                User.first_name.contains(search),
                User.last_name.contains(search),
                User.username.contains(search)
            )
        )
    
    users = users.order_by(User.first_name, User.last_name).limit(20).all()
    
    result = []
    for user in users:
        result.append({
            'id': user.id,
            'name': user.get_full_name(),
            'username': user.username,
            'email': user.email
        })
    
    return jsonify(result)
