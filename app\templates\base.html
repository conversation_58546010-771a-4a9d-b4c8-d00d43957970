<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}Sparkle Media Agency</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='img/logo.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='img/favicon.ico') }}">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

    {% block styles %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">
    {% if SystemConfig.is_announcement_bar_enabled() %}
    <!-- Announcement Bar -->
    <div class="announcement-bar py-2 text-center overflow-hidden" style="background-color: {{ SystemConfig.get_announcement_bg_color() }}; color: {{ SystemConfig.get_announcement_text_color() }};">
        <div class="container d-flex align-items-center justify-content-center">
            {% if SystemConfig.is_announcement_animated() %}
            <div class="animated-text">
                <i class="fas fa-bullhorn me-2"></i>{{ SystemConfig.get_announcement_text() }}
            </div>
            {% else %}
            <i class="fas fa-bullhorn me-2"></i>{{ SystemConfig.get_announcement_text() }}
            {% endif %}
        </div>
    </div>
    {% endif %}

    {% if current_user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-sparkles me-2"></i>Sparkle Media Agency
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#sidebarMenu" aria-controls="sidebarMenu" aria-expanded="false" aria-label="Toggle sidebar">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="d-flex">
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-circle me-1 text-{{ current_user.get_status_color() }}" style="font-size: 0.7em;"></i>
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li class="dropdown-item-text">
                                <small class="text-muted">الحالة: <span class="text-{{ current_user.get_status_color() }}" id="current-status-text">{{ current_user.get_status_display() }}</span></small>
                            </li>
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#statusModal">
                                <i class="fas fa-circle me-1"></i>تغيير الحالة
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('employee.view', id=current_user.id) }}">بيانات الموظف</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content with Sidebar -->
    <div class="container-fluid mt-4 flex-grow-1">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar" id="sidebarMenu">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('calendar.index') }}">
                                <i class="fas fa-calendar-alt me-1"></i>التقويم
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('employee.index') }}">
                                <i class="fas fa-users me-1"></i>الموظفين
                            </a>
                        </li>

                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('department_head') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('department.index') }}">
                                <i class="fas fa-building me-1"></i>الأقسام
                            </a>
                        </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('project.index') }}">
                                <i class="fas fa-project-diagram me-1"></i>المشاريع
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('leave.index') }}">
                                <i class="fas fa-calendar-alt me-1"></i>الإجازات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('meeting.index') }}">
                                <i class="fas fa-handshake me-1"></i>الاجتماعات
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('honorary_rank.index') }}">
                                <i class="fas fa-award me-1"></i>الرتب الشرفية
                            </a>
                        </li>

                        {% if current_user.has_role('admin') or current_user.has_role('manager') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('penalty.index') }}">
                                <i class="fas fa-exclamation-triangle me-1"></i>العقوبات
                            </a>
                        </li>
                        {% endif %}

                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('sales') or current_user.has_role('finance') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('client.index') }}">
                                <i class="fas fa-user-tie me-1"></i>العملاء
                            </a>
                        </li>
                        {% endif %}

                        {% if current_user.has_role('admin') or current_user.has_role('manager') or current_user.has_role('finance') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('finance.index') }}">
                                <i class="fas fa-money-bill-wave me-1"></i>المالية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('finance.transactions') }}">
                                <i class="fas fa-exchange-alt me-1"></i>المعاملات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('finance.invoices') }}">
                                <i class="fas fa-file-invoice-dollar me-1"></i>الفواتير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('report.index') }}">
                                <i class="fas fa-chart-bar me-1"></i>التقارير
                            </a>
                        </li>
                        {% endif %}

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('notification.index') }}">
                                <i class="fas fa-bell me-1"></i>الإشعارات
                                <span class="badge bg-danger rounded-pill notification-count">0</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('mail.inbox') }}">
                                <i class="fas fa-envelope me-1"></i>البريد الداخلي
                                <span class="badge bg-danger rounded-pill mail-count">0</span>
                            </a>
                        </li>

                        {% if current_user.has_role('admin') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('system.config') }}">
                                <i class="fas fa-cogs me-1"></i>إعدادات النظام
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-auto">
        <div class="container">
            <p class="mb-0">&copy; 2025 Sparkle Media Agency. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% if current_user.is_authenticated %}
    <script>
        // Update notification count
        function updateNotificationCount() {
            $.get("{{ url_for('notification.get_unread_count') }}", function(data) {
                const count = data.count;
                const countElement = $('.notification-count');

                if (count > 0) {
                    countElement.text(count);
                    countElement.show();
                } else {
                    countElement.hide();
                }
            });
        }

        // Update mail count
        function updateMailCount() {
            $.get("{{ url_for('mail.get_unread_count') }}", function(data) {
                const count = data.count;
                const countElement = $('.mail-count');

                if (count > 0) {
                    countElement.text(count);
                    countElement.show();
                } else {
                    countElement.hide();
                }
            });
        }

        // Update counts on page load
        $(document).ready(function() {
            updateNotificationCount();
            updateMailCount();

            // Update counts every minute
            setInterval(function() {
                updateNotificationCount();
                updateMailCount();
            }, 60000);

            // Fix for delete modals
            $('.modal').on('shown.bs.modal', function (e) {
                $(this).data('bs.modal')._config.backdrop = 'static';
                $(this).data('bs.modal')._config.keyboard = false;
            });

            // Add active class to current nav item
            const currentPath = window.location.pathname;
            $('.sidebar .nav-link').each(function() {
                const linkPath = $(this).attr('href');
                if (currentPath === linkPath || currentPath.startsWith(linkPath) && linkPath !== '/') {
                    $(this).addClass('active');
                }
            });

            // Toggle sidebar on mobile
            $('.navbar-toggler').on('click', function() {
                $('.sidebar').toggleClass('show');
            });
        });
    </script>
    {% endif %}

    {% if current_user.is_authenticated %}
    <!-- Status Update Modal -->
    <div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statusModalLabel">تغيير حالة الاتصال</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="statusForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="online" {{ 'selected' if current_user.status == 'online' else '' }}>متصل</option>
                                <option value="busy" {{ 'selected' if current_user.status == 'busy' else '' }}>مشغول</option>
                                <option value="away" {{ 'selected' if current_user.status == 'away' else '' }}>ساكن</option>
                                <option value="offline" {{ 'selected' if current_user.status == 'offline' else '' }}>غير متصل</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="custom_message" class="form-label">رسالة مخصصة (اختيارية)</label>
                            <input type="text" class="form-control" id="custom_message" name="custom_message"
                                   value="{{ current_user.custom_status or '' }}" maxlength="255"
                                   placeholder="أدخل رسالة مخصصة...">
                            <div class="form-text">يمكنك إضافة رسالة مخصصة لتوضيح حالتك</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">تحديث الحالة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Handle status form submission
        document.getElementById('statusForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('{{ url_for("auth.update_status") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status display in navbar
                    document.getElementById('current-status-text').textContent = data.status;
                    document.getElementById('current-status-text').className = 'text-' + data.color;

                    // Update status indicator
                    const statusIndicator = document.querySelector('#userDropdown .fa-circle');
                    statusIndicator.className = 'fas fa-circle me-1 text-' + data.color;
                    statusIndicator.style.fontSize = '0.7em';

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('statusModal'));
                    modal.hide();

                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show';
                    alertDiv.innerHTML = `
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    document.querySelector('main').insertBefore(alertDiv, document.querySelector('main').firstChild);
                } else {
                    alert('خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث الحالة');
            });
        });
    </script>
    {% endif %}

    {% block scripts %}{% endblock %}
</body>
</html>
