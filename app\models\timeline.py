from datetime import datetime
from app import db

class ProjectTimeline(db.Model):
    """نموذج التايم لاين للمشاريع"""
    __tablename__ = 'project_timelines'

    id = db.Column(db.Integer, primary_key=True)
    name = db.<PERSON>umn(db.String(255), nullable=False)  # اسم التايم لاين
    description = db.Column(db.Text)  # وصف التايم لاين
    is_active = db.Column(db.<PERSON>, default=True)  # هل التايم لاين نشط
    order_index = db.Column(db.Integer, default=0)  # ترتيب التايم لاين في المشروع
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project_id = db.Column(db.Inte<PERSON>, db.<PERSON>ey('projects.id'), nullable=False)
    project = db.relationship('Project', backref=db.backref('timelines', lazy='dynamic', cascade='all, delete-orphan'))
    
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='created_timelines')

    # خطوات التايم لاين
    steps = db.relationship('TimelineStep', backref='timeline', lazy='dynamic', 
                           cascade='all, delete-orphan', order_by='TimelineStep.order_index')

    def __repr__(self):
        return f'<ProjectTimeline {self.id}: {self.name}>'

    def get_steps_ordered(self):
        """الحصول على الخطوات مرتبة"""
        return self.steps.order_by(TimelineStep.order_index).all()

    def get_completion_percentage(self):
        """حساب نسبة الإنجاز"""
        total_steps = self.steps.count()
        if total_steps == 0:
            return 0
        
        completed_steps = self.steps.filter_by(status='completed').count()
        return int((completed_steps / total_steps) * 100)


class TimelineStep(db.Model):
    """نموذج خطوة التايم لاين"""
    __tablename__ = 'timeline_steps'

    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(255), nullable=False)  # عنوان الخطوة
    description = db.Column(db.Text)  # وصف الخطوة
    status = db.Column(db.String(20), default='pending')  # pending, in_progress, completed, cancelled
    order_index = db.Column(db.Integer, default=0)  # ترتيب الخطوة في التايم لاين
    
    # نوع مصدر الخطوة
    source_type = db.Column(db.String(20), nullable=False)  # 'task' أو 'manual'
    
    # للخطوات اليدوية
    links = db.Column(db.Text)  # روابط مفصولة بفواصل
    
    # للخطوات المرتبطة بالمهام
    task_id = db.Column(db.Integer, db.ForeignKey('tasks.id'))
    task = db.relationship('Task', backref='timeline_steps')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    timeline_id = db.Column(db.Integer, db.ForeignKey('project_timelines.id'), nullable=False)
    
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_by = db.relationship('User', backref='created_timeline_steps')

    def __repr__(self):
        return f'<TimelineStep {self.id}: {self.title}>'

    def get_links_list(self):
        """الحصول على قائمة الروابط"""
        if self.links:
            return [link.strip() for link in self.links.split(',') if link.strip()]
        return []

    def get_status_display(self):
        """الحصول على نص الحالة للعرض"""
        status_map = {
            'pending': 'معلق',
            'in_progress': 'قيد التنفيذ',
            'completed': 'مكتمل',
            'cancelled': 'ملغي'
        }
        return status_map.get(self.status, 'معلق')

    def get_status_color(self):
        """الحصول على لون الحالة"""
        color_map = {
            'pending': 'secondary',
            'in_progress': 'primary',
            'completed': 'success',
            'cancelled': 'danger'
        }
        return color_map.get(self.status, 'secondary')

    def get_status_icon(self):
        """الحصول على أيقونة الحالة"""
        icon_map = {
            'pending': 'fas fa-clock',
            'in_progress': 'fas fa-spinner',
            'completed': 'fas fa-check-circle',
            'cancelled': 'fas fa-times-circle'
        }
        return icon_map.get(self.status, 'fas fa-clock')

    def sync_with_task(self):
        """مزامنة الخطوة مع المهمة المرتبطة بها"""
        if self.source_type == 'task' and self.task:
            # تحديث العنوان والوصف من المهمة
            self.title = self.task.title
            self.description = self.task.description
            
            # تحديث الحالة من المهمة
            self.status = self.task.status
            
            return True
        return False

    def update_task_status(self):
        """تحديث حالة المهمة المرتبطة (إذا كانت موجودة)"""
        if self.source_type == 'task' and self.task:
            # تحديث حالة المهمة فقط إذا تم تغيير حالة الخطوة
            self.task.status = self.status
            if self.status == 'completed':
                self.task.completed_at = datetime.utcnow()
            return True
        return False
