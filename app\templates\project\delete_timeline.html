{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            حذف التايم لاين
                        </h4>
                        <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-right me-1"></i>العودة للتايم لاين
                        </a>
                    </div>
                </div>
                
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            تحذير: عملية لا يمكن التراجع عنها!
                        </h5>
                        <p class="mb-0">
                            أنت على وشك حذف التايم لاين "<strong>{{ timeline.name }}</strong>" نهائياً.
                            هذا الإجراء سيؤدي إلى حذف جميع الخطوات المرتبطة بهذا التايم لاين أيضاً.
                        </p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-info-circle me-1"></i>
                                        معلومات التايم لاين
                                    </h6>
                                    <p class="card-text">
                                        <strong>الاسم:</strong> {{ timeline.name }}
                                    </p>
                                    {% if timeline.description %}
                                    <p class="card-text">
                                        <strong>الوصف:</strong> {{ timeline.description }}
                                    </p>
                                    {% endif %}
                                    <p class="card-text">
                                        <strong>المشروع:</strong> {{ project.name }}
                                    </p>
                                    <p class="card-text">
                                        <strong>تاريخ الإنشاء:</strong> {{ timeline.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </p>
                                    <p class="card-text">
                                        <strong>منشئ التايم لاين:</strong> {{ timeline.created_by.get_full_name() }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h6 class="card-title text-warning">
                                        <i class="fas fa-list me-1"></i>
                                        الخطوات التي سيتم حذفها
                                    </h6>
                                    
                                    {% set steps_count = timeline.steps.count() %}
                                    {% if steps_count > 0 %}
                                    <p class="card-text">
                                        <strong class="text-danger">{{ steps_count }}</strong> خطوة سيتم حذفها نهائياً:
                                    </p>
                                    
                                    <div class="max-height-200 overflow-auto">
                                        <ul class="list-group list-group-flush">
                                            {% for step in timeline.steps %}
                                            <li class="list-group-item px-0 py-2">
                                                <div class="d-flex align-items-center">
                                                    <i class="{{ step.get_status_icon() }} me-2 text-{{ step.get_status_color() }}"></i>
                                                    <div class="flex-grow-1">
                                                        <div class="fw-bold">{{ step.title }}</div>
                                                        {% if step.description %}
                                                        <small class="text-muted">{{ step.description[:50] }}{% if step.description|length > 50 %}...{% endif %}</small>
                                                        {% endif %}
                                                        <div class="mt-1">
                                                            <span class="badge bg-{{ step.get_status_color() }}">{{ step.get_status_display() }}</span>
                                                            {% if step.source_type == 'task' %}
                                                            <span class="badge bg-info">مرتبط بمهمة</span>
                                                            {% else %}
                                                            <span class="badge bg-warning">خطوة يدوية</span>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                    {% else %}
                                    <p class="card-text text-muted">
                                        لا توجد خطوات في هذا التايم لاين.
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <div class="card bg-danger bg-opacity-10 border-danger">
                            <div class="card-body">
                                <h6 class="card-title text-danger">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    تأكيد الحذف
                                </h6>
                                <p class="card-text">
                                    للمتابعة مع حذف التايم لاين، يرجى التأكد من أنك تفهم العواقب:
                                </p>
                                <ul class="text-danger">
                                    <li>سيتم حذف التايم لاين نهائياً</li>
                                    <li>سيتم حذف جميع الخطوات ({{ timeline.steps.count() }} خطوة)</li>
                                    <li>لا يمكن التراجع عن هذا الإجراء</li>
                                    <li>البيانات المحذوفة لن تكون قابلة للاسترداد</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" class="mt-4">
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ url_for('project.timeline', id=project.id) }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-right me-1"></i>
                                    إلغاء والعودة
                                </a>
                                <a href="{{ url_for('project.edit_timeline', timeline_id=timeline.id) }}" class="btn btn-primary ms-2">
                                    <i class="fas fa-edit me-1"></i>
                                    تعديل بدلاً من الحذف
                                </a>
                            </div>
                            
                            <div>
                                <button type="submit" class="btn btn-danger" onclick="return confirmDelete()">
                                    <i class="fas fa-trash me-1"></i>
                                    نعم، احذف التايم لاين نهائياً
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.max-height-200 {
    max-height: 200px;
}
</style>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete() {
    const timelineName = "{{ timeline.name }}";
    const stepsCount = {{ timeline.steps.count() }};
    
    let message = `هل أنت متأكد من حذف التايم لاين "${timelineName}"؟\n\n`;
    message += `سيتم حذف ${stepsCount} خطوة نهائياً.\n`;
    message += `هذا الإجراء لا يمكن التراجع عنه!\n\n`;
    message += `اكتب "حذف" للتأكيد:`;
    
    const confirmation = prompt(message);
    
    if (confirmation === "حذف" || confirmation === "delete" || confirmation === "DELETE") {
        return true;
    } else {
        alert("تم إلغاء عملية الحذف");
        return false;
    }
}
</script>
{% endblock %}
